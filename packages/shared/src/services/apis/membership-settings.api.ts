import { baseApi } from './base.api'
import { IMembershipSetting } from '@/shared-types/interfaces'

export const getMembershipSettingApi = (membershipId: string) => {
  return baseApi().get(`/api/membership-setting`, { params: { membership_id: membershipId } })
}

export const updateMembershipSettingApi = (id: string, payload: Partial<IMembershipSetting>) => {
  return baseApi().put(`/api/membership-setting`, { id, ...payload })
}
