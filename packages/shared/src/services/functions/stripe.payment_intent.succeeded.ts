import { inngest } from '../inngest'
import { setupAndUpdateMembership, setupAndUpdateUser } from './stripe.invoice.paid'
import { STRIPE_SUBSCRIPTION_STATUS_ENUM } from '@memberup/shared/src/types/enum'

export default inngest.createFunction(
  { id: 'payment-intent-succeeded', name: 'Payment Intent Succeeded' },
  { event: 'stripe/payment_intent.succeeded' },
  async ({ event, step }) => {
    const { data } = event
    await step.run('payment-intent-succeeded', async () => {
      const stripeMetadataMode = data?.['metadata']?.['mode'] || ''
      if (data['isMembershipStripe']) {
        const profileUpdateData = {
          stripe_subscription_intent_client_secret: null,
          stripe_metadata_mode: stripeMetadataMode,
        }
        if (stripeMetadataMode === 'lifetime') {
          profileUpdateData['stripe_subscription_status'] = STRIPE_SUBSCRIPTION_STATUS_ENUM.active
        }

        await setupAndUpdateUser(data, profileUpdateData)
      } else {
        const membershipSettingUpdateData = {
          stripe_subscription_intent_client_secret: null,
          stripe_metadata_mode: stripeMetadataMode,
        }
        await setupAndUpdateMembership(data, membershipSettingUpdateData)
      }
    })
  }
)
