import moment from 'moment-timezone'
import { inngest } from '../inngest'
import {
  createSparkMemberhsipQuestion,
  findMembershipsWithSparkSettingsEnabled,
  findSparkQuestions,
  markSparkMembershipQuestionAsCompleted,
  setCurrentSparkMemberhsipQuestion,
} from '@/shared-libs/prisma/spark-question'
import { SPARK_MEMBERSHIP_QUESTION } from '@memberup/shared/src/types/enum'
import { createSparkResponse, getSparkCurrentStreak } from '@/shared-libs/prisma/spark-response'
import { knockSpark } from 'memberup/src/libs/knock'

const BATCH_SIZE = 10
const QUESTION_ROTATION_HOUR = '00'

export const runSparkQuestionRotate = inngest.createFunction(
  { id: 'run-spark-question-rotate', name: 'Run Spark Question Rotate' },
  { cron: '15 * * * *' },
  async ({ step }) => {
    await step.sendEvent('send-spark.next-question-event', {
      name: 'memberup/spark.next-question',
      data: {},
    })
  }
)

export async function nextSparkQuestion(
  activeCategoryId: string,
  activeMembershipCategoryId: string,
  questionSequences: string[],
  oldMembershipQuestionId: string,
  ownerId: string,
  membershipId: string
) {
  const sparkQuestions = await findSparkQuestions(membershipId, activeCategoryId)
  if (!sparkQuestions || sparkQuestions.length === 0) {
    console.warn('No questions found.')
    return null
  }

  questionSequences = questionSequences || []
  let questionSequencesCopy = [...questionSequences]
  questionSequencesCopy.sort()
  let sparkQuestionsIds = sparkQuestions.map((q) => q.id)
  sparkQuestionsIds.sort()

  if (JSON.stringify(questionSequencesCopy) !== JSON.stringify(sparkQuestionsIds)) {
    questionSequences = sparkQuestions.map((q) => q.id)
  }
  sparkQuestions.sort((a, b) => {
    const aIndex = questionSequences.indexOf(a.id)
    if (aIndex === -1) {
      throw new Error(`Question id ${a.id} not found in ordering list`)
    }
    const bIndex = questionSequences.indexOf(b.id)
    if (bIndex === -1) {
      throw new Error(`Question id ${b.id} not found in ordering list`)
    }
    return aIndex - bIndex
  })

  // Select the next active question
  const selectedQuestion = sparkQuestions.find((q) => q.active)
  if (!selectedQuestion) {
    console.warn(`There isn't any active question to choose from.`)
    return null
  }

  // Reorganize the sequences putting the selected question at the bottom of the sequence.
  const selectedQuestionIndex = questionSequences.findIndex((qs) => qs === selectedQuestion.id)
  questionSequences = [
    ...questionSequences.slice(selectedQuestionIndex + 1),
    ...questionSequences.slice(0, selectedQuestionIndex + 1),
  ]

  // Create the current membership question
  const newMembershipQuestion = await createSparkMemberhsipQuestion({
    data: {
      question_id: selectedQuestion.id,
      membership_id: membershipId,
      status: SPARK_MEMBERSHIP_QUESTION.started,
    },
    select: {
      id: true,
    },
  })

  if (selectedQuestion.answer) {
    let streakCount = await getSparkCurrentStreak(ownerId, membershipId)
    streakCount += 1
    await createSparkResponse(
      {
        data: {
          content: selectedQuestion.answer,
          membership_id: membershipId,
          question: {
            connect: {
              id: selectedQuestion.id,
            },
          },
          m_question: {
            connect: {
              id: newMembershipQuestion.id,
            },
          },
          user: {
            connect: {
              id: ownerId,
            },
          },
          streak_count: streakCount,
        },
      },
      activeMembershipCategoryId
    )
  }

  // Mark old question as completed
  if (oldMembershipQuestionId) {
    await markSparkMembershipQuestionAsCompleted(oldMembershipQuestionId)
  }

  // Set the current question to the selected one.
  await setCurrentSparkMemberhsipQuestion(
    activeMembershipCategoryId,
    selectedQuestion.id,
    newMembershipQuestion.id,
    questionSequences
  )

  // Notify users about new spark question
  await knockSpark({
    id: selectedQuestion.id,
    question: selectedQuestion.content,
    membership_id: membershipId,
    user_id: ownerId,
  })

  return newMembershipQuestion
}

export default inngest.createFunction(
  { id: 'spark-question-rotate', name: 'Spark Question Rotate' },
  { event: 'memberup/spark.next-question' },
  async ({ event, step }) => {
    const memberships = await step.run('select-spark-enabled-memberships', async () => {
      const isTimeZoneAtHour = (timeZone, hour) => {
        const timeInZone = moment().tz(timeZone)
        return timeInZone.format('HH') === `${hour}`
      }
      const membershipsWithSparkSettings = await findMembershipsWithSparkSettingsEnabled()
      const filtered = membershipsWithSparkSettings.filter((m) => {
        if (event.data['memberships'] && !event.data['memberships'].includes(m.membership_id)) {
          return false
        }

        if (event.data['ignoreTimeZones']) {
          return true
        }
        return isTimeZoneAtHour(m.time_zone, QUESTION_ROTATION_HOUR)
      })
      console.log(filtered)
      return filtered
    })

    const chunkArray = (array, size) => {
      const chunkedArr = []
      for (let i = 0; i < array.length; i += size) {
        chunkedArr.push(array.slice(i, i + size))
      }
      return chunkedArr
    }

    const membershipBatches = chunkArray(memberships, BATCH_SIZE)

    const results = await Promise.all(
      membershipBatches.map((membershipBatch) =>
        step.run('process-next-question-batch', async () => {
          const output = []
          for (const membershipSparkSettings of membershipBatch) {
            const activeCategoryId = membershipSparkSettings['active_spark_category_id']
            const activeMembershipCategoryId = membershipSparkSettings['m_category_id']
            const oldMembershipQuestionId = membershipSparkSettings['active_m_question_id']
            const ownerId = membershipSparkSettings.owner
            const membershipId = membershipSparkSettings.membership_id
            const questionSequences = membershipSparkSettings['question_sequences']
            const newMembershipQuestion = await nextSparkQuestion(
              activeCategoryId,
              activeMembershipCategoryId,
              questionSequences,
              oldMembershipQuestionId,
              ownerId,
              membershipId
            )
            if (!newMembershipQuestion) {
              continue
            }

            output.push({
              membership_id: membershipSparkSettings.membership_id,
              next_question: newMembershipQuestion.id,
            })
          }
          return output
        })
      )
    )
    return results
  }
)
