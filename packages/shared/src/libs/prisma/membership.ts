import prisma from './prisma'
import { IMembership } from '@/shared-types/interfaces'
import { Prisma } from '@prisma/client'
import { createDefaultLibraryAndCourse } from '@memberup/shared/src/libs/prisma/content-library'

export async function createMembership(payload: Prisma.MembershipCreateArgs) {
  // payload: SelectSubset<MembershipCreateArgs>
  const result = await prisma.membership.create(payload)
  await createDefaultLibraryAndCourse(result.id)
  return result as IMembership
}

export async function findMembership(payload: Prisma.MembershipFindFirstArgs) {
  const result = await prisma.membership.findFirst(payload)
  return result as IMembership
}

export async function findMembershipById(payload: Prisma.MembershipFindUniqueArgs) {
  const result = await prisma.membership.findUnique(payload)
  return result as IMembership
}

export async function findMembershipBySlug(slug: string) {
  const result = await prisma.membership.findFirst({
    where: {
      slug,
    },
  })
  return result as IMembership
}

export async function findManyMemberships(payload: Prisma.MembershipFindManyArgs) {
  const result = await prisma.membership.findMany(payload)
  return result
}

export async function findMemberships(payload: Prisma.MembershipFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.membership.findMany(args)
  const total = await prisma.membership.count({ where })
  return { docs: result as IMembership[], total }
}

export async function updateMembership(payload: Prisma.MembershipUpdateArgs) {
  const result = await prisma.membership.update(payload)
  return result as IMembership
}

export async function deleteMembershipById(id: string) {
  const result = await prisma.membership.delete({
    where: {
      id,
    },
  })
  return result as IMembership
}
