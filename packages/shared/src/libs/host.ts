import memoizeOne from 'memoize-one'

export const getSlug = memoizeOne((host: string): string => {
  if (!host) return host || ''
  const temp = host.replace('www.', '').split('.')
  return temp[0]
})

export const getHost = memoizeOne((host: string, env: string): string => {
  let temp: string[] = host.split(':')
  const isLocalhost = temp[0].includes('.localhost')
  const isNgrok = temp[0].includes('.ngrok.io')

  const hosts = {
    production: '.memberup.com',
    development: '.memberup-dev.com',
    staging: '.memberup-staging.com',
  }
  const updatedHost = hosts[env]

  if (isLocalhost || isNgrok) {
    return temp[0]
      .replace(isLocalhost ? '.localhost' : '.ngrok.io', updatedHost)
      .replace('www.', '')
  }
  return temp[0].replace('www.', '')
})
