import * as Sentry from '@sentry/nextjs'
import _uniq from 'lodash/uniq'
import {getCommunityBaseURL} from 'memberup/src/libs/utils'

import {findMembershipById} from './prisma/membership'
import {deleteRecordsFromAlgoliaIndex, updateAlgoliaMembersIndexForUserId,} from '@/shared-libs/algolia'
import {
  KNOCK_OBJECT_IDS,
  knockBulkAddSubscriptions,
  knockDeleteUser,
  knockIdentifyUser,
  knockSetUserPreferences,
  knockTriggerWorkflow,
} from '@/shared-libs/knock'
import {findMembershipSetting} from '@/shared-libs/prisma/membership-settings'
import prisma from '@/shared-libs/prisma/prisma'
import {deleteUserById, findUser, findUsers, updateUser} from '@/shared-libs/prisma/user'
import {findUserMemberships} from '@/shared-libs/prisma/user-membership'
import {getFullName} from '@/shared-libs/profile'
import {streamChatDeactiveUser, streamChatUpsertUser} from '@/shared-libs/stream-chat'
import {stripeCancelSubscription} from '@/shared-libs/stripe'
import {KNOCK_WORKFLOW_ENUM, USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM, USER_STATUS_ENUM} from '@/shared-types/enum'
import {IMembership, IMembershipSetting, IUser} from '@/shared-types/interfaces'

// TODO: CHECK THIS CODE
const MU_ID = process.env.NEXT_PUBLIC_MU_ID

export const generateUsername = (firstName, lastName) => {
  const sanitizeString = (str) => str.replace(/[^a-zA-Z0-9]/g, '')

  // Sanitize the first and last names
  firstName = sanitizeString(firstName)
  lastName = sanitizeString(lastName)
  // Create an array with the non-empty first and last names
  let parts = [firstName, lastName].filter((name) => name !== '' && name !== null)
  // Join the parts with a dash if both are present
  let baseUsername = parts.join('-')
  // Generate a random 5-character alphanumeric string
  let randomString = Math.random().toString(36).substring(2, 7)
  // Create the final username
  let username = baseUsername ? `${baseUsername}-${randomString}` : randomString
  return username.toLowerCase()
}

// TODO 3023: Refactor this!

export const generateEmailVerificationCode = () => Math.floor(100000 + Math.random() * 900000).toString()

export const sendEmailVerificationCode = async (user: Partial<IUser>, code: string) => {
  await knockTriggerWorkflow(KNOCK_WORKFLOW_ENUM.verify_email, [user.id], {
    verification_code: code,
  })
}

const NOTIFICATION_SETTINGS = [
  {
    id: 'mention-notification',
    title: 'New Mention',
    name: 'new-mention-notification',
    email: true,
    email_disabled: false,
    in_app_feed: true,
  },
  {
    id: 'event',
    title: 'Event reminders',
    name: 'event',
    email: true,
    in_app_feed: true,
    email_disabled: false,
  },
  {
    id: 'new-everyone-mention-notification',
    title: 'Everyone Mention',
    name: 'new-everyone-mention-notification',
    email: true,
    email_disabled: false,
    in_app_feed: true,
  },
  {
    id: 'new-comment-notification',
    title: 'Comments on my posts',
    name: 'new-comment-notification',
    email: true,
    in_app_feed: true,
    email_disabled: false,
  },
]

export const setupUserOnExternalServices = async (
  user: any,
  membership: any,
  membershipSetting: any,
  isNew: boolean = true,
) => {
  try {
    await knockIdentifyUser(user)

    if (isNew) {
      // Subscribe user to knock notification objects.
      // TODO: Change the Knock collection name to be the membership ID instead as the slug can change.
      await knockBulkAddSubscriptions(membership.slug, KNOCK_OBJECT_IDS, [user.id])

      const preferenceSet = {
        workflows: {},
      }

      try {
        for (const notificationSetting of NOTIFICATION_SETTINGS) {
          preferenceSet.workflows[notificationSetting.name] = {
            channel_types: {
              email: notificationSetting.email,
              in_app_feed: notificationSetting.in_app_feed,
            },
          }
        }
        await knockSetUserPreferences(user.id, preferenceSet, {
          preferenceSet: membership.id,
        })
      } catch (e) {
        console.warn(e)
      }
    }

    // Add user to the Algolia index
    await updateAlgoliaMembersIndexForUserId(user.id)

    const userMemberships = await findUserMemberships({
      where: {
        user_id: user.id,
        status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
      },
      select: {
        membership_id: true,
      },
    })
    const teams = userMemberships.map((um) => um.membership_id)
    await streamChatUpsertUser(
      {
        id: user.id,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        image: user.profile?.image || user.image,
        image_crop_area: user.profile?.image_crop_area || null,
        status: user.status,
        username: user.username,
      },
      teams,
    )
    return true
  } catch (err) {
    Sentry.captureException(err)
    console.error('userSetup =======', err)
    return false
  }
}

export const setupUser = async (
  user: Partial<IUser>,
  membership: IMembership,
  membershipSetting: IMembershipSetting,
  isNew: boolean = true,
  shouldUpdateKnockUser: boolean = true,
  shouldUpdateStreamChatUser: boolean = true,
  shouldSendWelcomeEmail: boolean = true,
) => {
  try {
    const membershipSlug = membership.slug
    const membershipId = membership.id

    if (shouldUpdateKnockUser) {
      await knockIdentifyUser(user)
    }

    if (isNew) {
      await knockBulkAddSubscriptions(membershipSlug, KNOCK_OBJECT_IDS, [user.id])
      const preferenceSet = {
        workflows: {},
      }

      for (const notificationSetting of NOTIFICATION_SETTINGS) {
        preferenceSet.workflows[notificationSetting.name] = {
          channel_types: {
            email: notificationSetting.email,
            in_app_feed: notificationSetting.in_app_feed,
          },
        }
      }
      await knockSetUserPreferences(user.id, preferenceSet, {
        preferenceSet: membershipId,
      })
    }
    const communityUrl = `${getCommunityBaseURL(membership)}`

    if (shouldSendWelcomeEmail) {
      const enableWelcomeEmailNotice =
        membershipSetting.emails?.find((e) => e.name === 'welcome_email')?.enable !== false

      if (enableWelcomeEmailNotice) {
        const communityUrl = getCommunityBaseURL(membership)
        const owner = await prisma.user.findFirst({
          where: {
            membership_id: membershipId,
            role: { in: [USER_ROLE_ENUM.owner] },
            id: { not: user.id },
            status: USER_STATUS_ENUM.active,
          },
        })
        knockTriggerWorkflow(
          KNOCK_WORKFLOW_ENUM.new_member_welcome,
          [user.id],
          {
            community_name: membership.name,
            creator_name: getFullName(owner.first_name, owner.last_name),
            login_url: `${communityUrl}`,
          },
          owner.id,
          membership.id,
        )
      }
    }

    if (isNew) {
      const isNewMemberNotificationEnabled =
        membershipSetting.emails?.find((e) => e.name === 'new_member_signup_email')?.enable !== false
      if (isNewMemberNotificationEnabled) {
        const creators = await findUsers({
          where: {
            membership_id: membershipId,
            role: { in: [USER_ROLE_ENUM.owner, USER_ROLE_ENUM.admin] },
            id: { not: user.id },
            status: USER_STATUS_ENUM.active,
          },
        })
        const idempotencyRequestKey = `new-member-notification-${user.membership_id}-${user.id}`
        knockTriggerWorkflow(
          KNOCK_WORKFLOW_ENUM.new_member,
          creators.docs.map((c) => c.id),
          {
            community_name: membership.name,
            community_url: communityUrl,
          },
          user.id,
          membership.id,
          null,
          idempotencyRequestKey,
        )
      }
    }

    if (shouldUpdateStreamChatUser) {
      const isNotMember = [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(
        user.role || (USER_ROLE_ENUM.member as any),
      )
      await streamChatUpsertUser(
        {
          id: user.id,
          first_name: user.first_name || '',
          last_name: user.last_name || '',
          image: user.profile?.image || user.image,
          image_crop_area: user.profile?.image_crop_area || null,
          role: user.role,
          status: user.status,
          username: user.username,
        },
        isNotMember ? _uniq([MU_ID, user.membership_id]) : [user.membership_id],
      )
    }
    return true
  } catch (err) {
    console.error('userSetup =======', err)
    return false
  }
}

export const sendCancelMembershipEmail = async (userId: string) => {
  try {
    const user: IUser = await findUser({
      where: { id: userId },
    })

    const membership = await findMembershipById({
      where: { id: user.membership_id },
    })

    await knockTriggerWorkflow(
      KNOCK_WORKFLOW_ENUM.cancel_membership,
      [userId],
      {
        community_name: membership.name,
      },
      user.id,
      membership.id,
    )
  } catch (error) {
    console.error(error)
    Sentry.captureException(error)
    return null
  }
}

export const cancelMember = async (
  userId: string,
  status: USER_STATUS_ENUM,
  forceCancel?: boolean,
  banned_reason?: string,
) => {
  try {
    const oldUser: IUser = await findUser({
      where: { id: userId },
      include: {
        profile: true,
        membership: true,
      },
    })

    await findMembershipById({
      where: { id: oldUser.membership_id },
    })

    if (oldUser.status === USER_STATUS_ENUM.invited) {
      const result = await deleteUserById(oldUser.id)
      return result
    }

    const oldUserProfile = oldUser?.profile
    const data = {
      status,
      banned_reason,
      profile: {
        update: { active: false, stripe_payment_method_id: null },
      },
    }
    const result = await updateUser({
      where: { id: userId },
      data,
      include: { profile: true },
    })

    const isAdminOrOwner = [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(
      result.role || (USER_ROLE_ENUM.member as any),
    )
    await streamChatDeactiveUser(userId)
    await streamChatUpsertUser(
      {
        id: result.id,
        first_name: result.first_name || '',
        last_name: result.last_name || '',
        image: result.profile?.image || result.image,
        image_crop_area: result.profile?.image_crop_area || null,
        role: result.role,
        status: result.status,
      },
      isAdminOrOwner ? _uniq([MU_ID, result.membership_id]) : [result.membership_id],
    )
    await deleteRecordsFromAlgoliaIndex('member', [userId])
    await knockDeleteUser(userId)

    if (oldUserProfile?.stripe_subscription_id) {
      const membershipSetting = await findMembershipSetting({
        where: { membership_id: oldUser.membership_id },
      })

      // NOTE: There is an edge case where if we disconnect the stripe account, then there will be users with subscriptions running.
      const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account
      if (connectedStripeAccountInfo) {
        await stripeCancelSubscription(connectedStripeAccountInfo, oldUserProfile.stripe_subscription_id, false)
      }
    }

    return result || oldUser
  } catch (err) {
    console.error(err)
    Sentry.captureException(err)
    return null
  }
}
