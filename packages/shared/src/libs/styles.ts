import { DefaultThemeOptions, colorOptions } from '../settings/theme'
import { THEME_MODE_ENUM } from '../types/enum'
import { gradientToArray, hexToRGBA } from './color'
import { ThemeOptions } from '@mui/material/styles'
import _cloneDeep from 'lodash/cloneDeep'
import _merge from 'lodash/merge'
import _set from 'lodash/set'
import memoizeOne from 'memoize-one'

export const getThemeOptions = memoizeOne(
  (
    themeMode: THEME_MODE_ENUM,
    themeOptions: Partial<ThemeOptions>,
    mainColor: string,
    secondaryColor?: string
  ) => {
    mainColor =
      mainColor.indexOf('linear-gradient') === 0 ? gradientToArray(mainColor)[1]?.color : mainColor
    mainColor = mainColor.indexOf('#') === 0 ? hexToRGBA(mainColor) : mainColor
    secondaryColor =
      secondaryColor ||
      colorOptions.find((c) => c.primary === mainColor)?.secondary ||
      colorOptions[0].secondary

    const rgba = mainColor.match(
      /^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i
    )
    const primaryHoverColor = `rgba(${rgba?.[1] || 0}, ${rgba?.[2] || 0}, ${rgba?.[3] || 0}, 0.9)`
    const primaryDisabledColor = `rgba(${rgba?.[1] || 0}, ${rgba?.[2] || 0}, ${
      rgba?.[3] || 0
    }, 0.5)`
    const buttonDisabledColor =
      themeMode === THEME_MODE_ENUM.dark
        ? `rgba(${rgba?.[1] || 0}, ${rgba?.[2] || 0}, ${rgba?.[3] || 0}, 0.5)`
        : '#8D94A3'
    const temp = _merge(_cloneDeep(DefaultThemeOptions), themeOptions)

    temp.components.MuiStepIcon.styleOverrides.text['fill'] = mainColor
    temp.palette.primary['main'] = mainColor
    temp.palette.primary['dark'] = mainColor

    _set(
      temp,
      [
        'components',
        'MuiButton',
        'styleOverrides',
        'containedPrimary',
        '&:hover',
        'backgroundColor',
      ],
      primaryHoverColor
    )
    _set(
      temp,
      [
        'components',
        'MuiButton',
        'styleOverrides',
        'containedPrimary',
        '&:focus',
        'backgroundColor',
      ],
      primaryHoverColor
    )
    _set(
      temp,
      [
        'components',
        'MuiListItem',
        'styleOverrides',
        'root',
        '&.text-only',
        '&.Mui-selected',
        'color',
      ],
      primaryHoverColor
    )
    _set(
      temp,
      [
        'components',
        'MuiCssBaseline',
        'styleOverrides',
        'body',
        '& .background-color00',
        'backgroundColor',
      ],
      primaryHoverColor
    )
    _set(
      temp,
      [
        'components',
        'MuiButton',
        'styleOverrides',
        'containedPrimary',
        '&.Mui-disabled',
        'backgroundColor',
      ],
      primaryDisabledColor
    )
    _set(
      temp,
      ['components', 'MuiButton', 'styleOverrides', 'containedPrimary', '&.Mui-disabled', 'color'],
      buttonDisabledColor
    )
    _set(
      temp,
      ['components', 'MuiStepIcon', 'styleOverrides', 'textPrimary', '&.Mui-disabled', 'color'],
      buttonDisabledColor
    )
    _set(
      temp,
      [
        'components',
        'MuiCssBaseline',
        'styleOverrides',
        'body',
        '& .background-gradient01',
        'background',
      ],
      secondaryColor
    )
    _set(
      temp,
      [
        'components',
        'MuiListItem',
        'styleOverrides',
        'root',
        '&.background-gradient.Mui-selected, &.background-gradient:hover',
        'background',
      ],
      secondaryColor
    )
    _set(
      temp,
      [
        'components',
        'MuiMenuItem',
        'styleOverrides',
        'root',
        '&.background-gradient:hover',
        'background',
      ],
      secondaryColor
    )
    _set(
      temp,
      ['components', 'MuiTabs', 'styleOverrides', 'indicator', 'backgroundColor'],
      mainColor
    )
    _set(
      temp,
      [
        'components',
        'MuiTab',
        'styleOverrides',
        'root',
        '&.MuiTab-textColorPrimary.Mui-selected',
        'color',
      ],
      mainColor
    )
    return temp
  }
)
