import Color from 'color'

const lightenColor = (color: string, percentage: number) => {
  return Color(color)
    .lighten(percentage / 100)
    .hex()
}

export const hexToRGBA = (hex, opacity = 1) => {
  if (!hex || hex.indexOf('#') === -1) return hex
  const tempHex = hex.replace('#', '')
  const r = parseInt(tempHex.substring(0, 2), 16)
  const g = parseInt(tempHex.substring(2, 4) || '00', 16)
  const b = parseInt(tempHex.substring(4, 6) || '00', 16)
  return `rgba(${r},${g},${b},${Math.min(opacity, 1)})`
}

export const rgbaToHex = (color: string) => {
  if (!color || color.indexOf('rgb') === -1) return color
  const tempColor = color?.match(
    /^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i
  )
  return tempColor?.length === 4
    ? '#' +
        ('0' + parseInt(tempColor[1], 10).toString(16)).slice(-2) +
        ('0' + parseInt(tempColor[2], 10).toString(16)).slice(-2) +
        ('0' + parseInt(tempColor[3], 10).toString(16)).slice(-2)
    : ''
}

export const gradientToArray = (color: string) => {
  const result = []
  if (color?.indexOf('linear-gradient') !== 0) return result
  const temp = color.split('deg,')
  const deg = temp[0].split('(')[1]
  result.push(parseInt(deg || '90'))

  if (!temp[1]) return result
  temp[1]
    .trim()
    .replace(/, /g, ',')
    .split('%,')
    .forEach((c) => {
      const t = c.trim().split(' ')
      result.push({ color: t[0].trim() })
    })
  return result
}

export const getGradientColor = (
  deg: number,
  color1: string,
  color2?: string,
  color3?: string,
  firstColorDeg: number = 0,
  secondColorDeg: number = 50,
  thirdColorDeg: number = 100
) => {
  if (!color2 && !color3) return color1
  const lightenedColor1Percentage = 0
  const lightenedColor2Percentage = 60
  const lightenedColor3Percentage = 50

  const lightenedColor1 = lightenColor(color1, lightenedColor1Percentage)
  const lightenedColor2 = color2 ? lightenColor(color2, lightenedColor2Percentage) : undefined
  const lightenedColor3 = color3 ? lightenColor(color3, lightenedColor3Percentage) : undefined

  let result = `linear-gradient(${deg || 45}deg, ${lightenedColor1} ${firstColorDeg}%,`
  if (lightenedColor3) {
    result += `${lightenedColor2} ${secondColorDeg}%, ${lightenedColor3} ${thirdColorDeg}%)`
  } else {
    result += `${lightenedColor2} ${secondColorDeg}%)`
  }

  return result
}

export const getMiddleColor = (color1: string, color2: string) => {
  color1 = hexToRGBA(color1)
  color2 = hexToRGBA(color2)
  const rgba1 = color1.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i)
  const rgba2 = color2.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i)
  const result = [
    Math.floor((parseInt(rgba1[1], 10) + parseInt(rgba2[1], 10)) / 2),
    Math.floor((parseInt(rgba1[2], 10) + parseInt(rgba2[2], 10)) / 2),
    Math.floor((parseInt(rgba1[3], 10) + parseInt(rgba2[3], 10)) / 2),
  ]
  return rgbaToHex(`rgba(${result[0]}, ${result[1]}, ${result[2]}, 1)`)
}

export const isValidHexaColor = (str: string) => {
  // let regex = new RegExp(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/);
  const regex = new RegExp(/^#([A-Fa-f0-9]{6})$/)

  if (str == null) {
    return false
  }

  return regex.test(str) === true
}

export const adjustRGBA = (rgba, newOpacity) => {
  const components = rgba.match(/[\d.]+/g)
  components[3] = newOpacity // replace the alpha component
  return `rgba(${components.join(',')})`
}
