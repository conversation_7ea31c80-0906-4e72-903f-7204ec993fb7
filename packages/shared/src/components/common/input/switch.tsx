import Switch from '@mui/material/Switch'
import { styled } from '@mui/material/styles'

const CustomSwitch = (props) => {
  return <Switch disableRipple {...props} />
}

export const AppSwitch = styled(CustomSwitch)(({ theme }) => {
  return {
    width: 48,
    height: 24,
    padding: 0,
    margin: 0,
    '& .MuiSwitch-switchBase': {
      padding: 0,
      top: 3,
      left: -2,
      transform: 'translateX(6px)',
      '&.Mui-checked': {
        transform: 'translateX(29px)',
        color: theme.palette.common.white,
        '&.Mui-disabled': {
          color: theme.palette.common.white,
        },
        '& + .MuiSwitch-track': {
          background: theme.palette.primary.dark,
          opacity: 1,
          border: 'none',
        },
      },
      '&.Mui-focusVisible .MuiSwitch-thumb': {
        color: '#52d869',
        borderWidth: 6,
        borderStyle: 'solid',
        borderColor: '#fff',
      },
    },
    '& .<PERSON>iSwitch-thumb': {
      width: 18,
      height: 18,
    },
    '& .MuiSwitch-track': {
      borderRadius: 12,
      borderStyle: 'solid',
      borderWidth: 1,
      borderColor: theme.palette.divider,
      backgroundColor: theme.palette.divider,
      opacity: 1,
      transition: theme.transitions.create(['background-color', 'border']),
    },
  }
})
