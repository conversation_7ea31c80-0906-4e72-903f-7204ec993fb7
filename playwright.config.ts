import { defineConfig, devices, type Config } from "@playwright/test";
import dotenv from "dotenv";

dotenv.config({ override: true });

const config: Config = {
  expect: { timeout: 20000 },
  use: {
    timezoneId: "America/Los_Angeles",
  },
};

if (process.env.PLAYWRIGHT_USE_STORAGE_STATE === "true") {
  config.projects = [
    // Setup project
    { name: "authsetup", testMatch: /.*\.setup\.ts/ },
    // Tests project
    {
      name: "chromium",
      use: {
        ...devices["Desktop Chrome"],
      },
      dependencies: ["authsetup"],
    },
  ];
}

export default defineConfig(config);
