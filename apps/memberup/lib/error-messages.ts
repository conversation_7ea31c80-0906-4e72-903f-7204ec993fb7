export const communityFetchError = 'An error occurred while fetching the community'

export const emailInUseError = 'There is already an existing user with this email.'

export const formSubmitError = 'An error occurred, please check your connection and try again.'

export const invalidPasswordError = 'Invalid password'

export const internalServerError = 'Internal Server Error'

export const getMinImageDimensionsError = (minWidth: number, minHeight: number) =>
  `Please select an image that is at least ${minWidth} x ${minHeight} pixels.`

export const unexpectedError = 'An unexpected error occurred.'
