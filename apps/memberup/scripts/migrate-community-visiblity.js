const { askQuestion, getStreamApps } = require('./common')
const { StreamChat } = require('stream-chat')
const { PrismaClient } = require('@prisma/client')

const isUUIDv4 = (uuid) => {
  // Regular expression pattern for UUID v4
  const uuidv4Pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  // Test if the provided string matches the UUID v4 pattern
  return uuidv4Pattern.test(uuid)
}

const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
  ],
})

prisma.$on('query', (e) => {
  //console.log(e.query, e.params)
})



const main = async () => {
  const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
  const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
  const DATABASE_URL = process.env.DATABASE_URL

  console.log(`Migrating communities visibilities.`)
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(
    `Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`
  )

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  await prisma.membershipSetting.updateMany({
    data: {
      visibility: 'public',
    },
    where: {
      visibility: 'open'
    }
  })

  await prisma.membershipSetting.updateMany({
    data: {
      visibility: 'private',
    },
    where: {
      visibility: 'closed'
    }
  })

}

main()
