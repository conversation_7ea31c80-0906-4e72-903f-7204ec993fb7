const fsLegacy = require('fs')
const fs = require('fs').promises
const { StreamChat } = require('stream-chat')
const { askQuestion, getStreamApps } = require('./common')
const readline = require('readline')
const path = require('path')

const processChunk = async (chunk, serverClient) => {
  const updateResponse = await serverClient.upsertUsers(chunk)
  console.log('Upserted users with ids:', Object.keys(updateResponse.users))
}
const processChannelExportFile = async (filePath, serverClient) => {
  const fileStream = fsLegacy.createReadStream(filePath)
  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity, // Handle \r, \n and \r\n as line breaks
  })

  for await (const line of rl) {
    try {
      const lineObj = JSON.parse(line)
      if (lineObj.type === 'channel') {
        lineObj.item.created_by_id = lineObj.item.created_by
        const channelType = lineObj.item.type
        const channelId = lineObj.item.id
        delete lineObj.item['created_by']
        delete lineObj.item['id']
        delete lineObj.item['type']
        console.log(`Creating channel ${channelType} ${channelId}`)
        const channel = serverClient.channel(channelType, channelId, lineObj.item)
        await channel.create()
      } else if (lineObj.type === 'message') {
        if (lineObj.item.type === 'deleted') continue

        const userId = lineObj.item.user
        lineObj.item['user_id'] = userId
        delete lineObj.item['user']
        delete lineObj.item['created_at']
        delete lineObj.item['updated_at']
        delete lineObj.item['deleted_at']
        if (lineObj.item['type'] === 'reply') delete lineObj.item['type'] // Just delete the type and let Getstream to set it correctly as a reply.

        try {
          const existingMessage = await serverClient.getMessage(lineObj.item.id)
          console.log('Updating message')
          serverClient.updateMessage(lineObj.item)
        } catch (e) {
          // Message does not exists, let's created it
          console.log('Creating message')
          const channel = serverClient.channel(lineObj.item.channel_type, lineObj.item.channel_id)
          await channel.sendMessage(lineObj.item)
        }
      }
    } catch (e) {
      console.error(`Error: ${e}`)
    }
  }
}

const processUsersFile = async (filePath, serverClient) => {
  const fileStream = fsLegacy.createReadStream(filePath)
  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity, // Handle \r, \n and \r\n as line breaks
  })

  let chunk = []
  for await (const line of rl) {
    const userLine = JSON.parse(line)
    const user = userLine.item
    delete user['created_at']
    delete user['updated_at']
    delete user['last_active']
    delete user['online']

    chunk.push(user)
    if (chunk.length >= 100) {
      await processChunk(chunk, serverClient)
      chunk = []
    }
  }

  // Process the remaining lines if any
  if (chunk.length > 0) {
    await processChunk(chunk, serverClient)
  }
}

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET

const serverClient = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
  timeout: 30000,
})

// Main function to initiate processing
const main = async () => {
  const exportFolderPath = process.argv[2]
  if (!exportFolderPath) {
    console.error('Please provide the export folder path.')
    process.exit(1)
  }

  console.log(`Running import process.`)
  console.log(
    `Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`
  )

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  try {
    const usersFilePath = path.join(exportFolderPath, 'users.json')
    await processUsersFile(usersFilePath, serverClient)

    try {
      const files = await fs.readdir(exportFolderPath)
      const regex = /^channel-export-\d+\.json$/
      for (let filename of files) {
        if (regex.test(filename)) {
          const filePath = path.join(exportFolderPath, filename)
          console.log()
          await processChannelExportFile(filePath, serverClient)
        }
      }
    } catch (err) {
      console.error(`Error: ${err}`)
    }
  } catch (err) {
    console.error('Error:', err)
  }
}

main()
