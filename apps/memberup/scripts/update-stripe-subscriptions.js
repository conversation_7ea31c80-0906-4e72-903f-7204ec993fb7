require('dotenv').config()

const {PrismaClient} = require('@prisma/client')
const Stripe = require('stripe')
const {askQuestion} = require("./common");

const DATABASE_URL = process.env.DATABASE_URL
const STRIPE_APPLICATION_FEE_BASIC = parseFloat(process.env.STRIPE_APPLICATION_FEE_BASIC)
const STRIPE_APPLICATION_FEE_PRO = parseFloat(process.env.STRIPE_APPLICATION_FEE_PRO)
const STRIPE_APPLICATION_FEE_ENTERPRISE = parseFloat(
    process.env.STRIPE_APPLICATION_FEE_ENTERPRISE
)

const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY
const STRIPE_SECRET_KEY_TEST = process.env.STRIPE_SECRET_KEY_TEST
const NEXT_PUBLIC_STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE

const prisma = new PrismaClient()
const stripe = new Stripe(
    NEXT_PUBLIC_STRIPE_LIVE_MODE === 'true' ? STRIPE_SECRET_KEY : STRIPE_SECRET_KEY_TEST,
    {
        apiVersion: '2023-10-16',
        maxNetworkRetries: 2,
    }
)


const updateSubscriptions = async (stripe, connectedAccountId, subscriptions, newApplicationFeePercent, membershipId) => {
    for (let subscription of subscriptions) {
        if (subscription.status !== 'active') {
            console.log(`Ignoring inactive subscription ${subscription.id} of membership ${membershipId}`)
            return
        }

        if (newApplicationFeePercent === subscription.application_fee_percent) {
            continue
        }
        try {
            const updatedSubscription = await stripe.subscriptions.update(
                subscription.id,
                {application_fee_percent: newApplicationFeePercent},
                {stripeAccount: connectedAccountId}
            )

            console.log('Updated Subscription:', {
                membership_id: membershipId,
                id: updatedSubscription.id,
                old_application_fee_percent: subscription.application_fee_percent,
                new_application_fee_percent: updatedSubscription.application_fee_percent
            })
        } catch (e) {
            console.error(e)
        }
    }
}


const retrieveAllSubscriptions = async (stripe, connectedAccountId) => {
    let hasMore = true;
    let lastSubscriptionId = null;
    const allSubscriptions = [];

    while (hasMore) {
        const params = {limit: 100};

        if (lastSubscriptionId) {
            params.starting_after = lastSubscriptionId;
        }
        const response = await stripe.subscriptions.list(
            params,
            {stripeAccount: connectedAccountId}

        );

        allSubscriptions.push(...response.data);
        lastSubscriptionId = response.data.length > 0 ? response.data[response.data.length - 1].id : null;
        hasMore = response.has_more;
    }
    return allSubscriptions;
};

const updateMembershipStripeSubscriptions = async (stripe, membershipSetting) => {
    const plan = membershipSetting.plan
    if (!(membershipSetting.stripe_connect_account?.stripe_user_id && (membershipSetting.stripe_connect_account?.enabled || membershipSetting.stripe_connect_account?.access_token))) {
        console.log(`Stripe is not enabled for membership ${membershipSetting.membership_id}`)
        return
    }
    const connectedAccountId = membershipSetting.stripe_connect_account.stripe_user_id
    const applicationFeePercent =
        plan === "enterprise"
            ? STRIPE_APPLICATION_FEE_ENTERPRISE
            : plan === "pro"
                ? STRIPE_APPLICATION_FEE_PRO
                : STRIPE_APPLICATION_FEE_BASIC

    const allSubscriptions = await retrieveAllSubscriptions(stripe, connectedAccountId)
    await updateSubscriptions(stripe, connectedAccountId, allSubscriptions, applicationFeePercent, membershipSetting.membership_id)

}

const main = async () => {
    const membershipId = process.argv[2] // Accept file path as a command-line argument
    console.log(
        `Running update stripe members subscription process for ${
            membershipId ? 'membership_id:' + membershipId : 'all communities'
        }`
    )

    console.log(`Using Database URL ${DATABASE_URL}`)

    const answer = await askQuestion('Do you want to proceed? (y/n): ')
    if (answer.toLowerCase() !== 'y') {
        console.log('Good bye.')
        return
    }

    let membershipsIds = []
    if (membershipId) {
        membershipsIds = membershipId.split(',')
    }

    const where = membershipsIds.length > 0 ? ({
        membership_id: {in: membershipsIds}
    }) : {}

    const membershipSettings = await prisma.membershipSetting.findMany({
        where
    })
    for (let membershipSetting of membershipSettings) {
        try {        // const answer = await askQuestion(`Going to process ${membershipSetting.membership_id}. Do you want to proceed? (y/n): `)
            // if (answer.toLowerCase() !== 'y') {
            //     console.log('Ignoring.')
            //     continue
            // }
            await updateMembershipStripeSubscriptions(stripe, membershipSetting)
        } catch (e) {
            console.error(e)
        }
    }
}

main()
