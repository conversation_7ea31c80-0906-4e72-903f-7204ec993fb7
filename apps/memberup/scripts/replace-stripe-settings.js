// Fetches ACTIONS_REPLACE_STRIPE_SETTINGS environment variable, containing a JSON config
// and replaces specific communities' and users' Stripe settings. It also removes the available stripe
// settings for every other community and user.
//
// JSON config format:
// {
//  "communities": {
//    "community-slug": {
//      "stripe_connect_account": [value],
//      "stripe_customer_id": [value],
//      ...
//      "stripe_subscription_status": [value]
//    }
//  },
//  "users": {
//    "user-id": {
//      "stripe_customer_id": [value],
//      "stripe_metadata_mode": [value],
//      ...
//      "stripe_subscription_status": [value]
//    }
//  }
// }
//
// Values not set on the JSON config will be set to null.
//
// Add --yes parameter for unattended operation
//
// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/replace-stripe-settings.js
const { askQuestion } = require("./common");
const { PrismaClient } = require("@prisma/client");

require('dotenv').config()

const prisma = new PrismaClient();
const { ACTIONS_REPLACE_STRIPE_SETTINGS, NEXT_PUBLIC_ENV } = process.env;

let settings = null;

const communityStripeFields = [
  "stripe_connect_account", "stripe_customer_id", "stripe_enable_annual", "stripe_enable_annual", "stripe_live_mode",
  "stripe_metadata_mode", "stripe_payment_method_id", "stripe_product_id", "stripe_subscription_canceled_at",
  "stripe_subscription_discount_id", "stripe_subscription_id", "stripe_subscription_intent_client_secret",
  "stripe_subscription_invoice_id", "stripe_subscription_period_end_at", "stripe_subscription_period_start_at",
  "stripe_subscription_status"
];

const userStripeFields = [
  "stripe_customer_id", "stripe_metadata_mode", "stripe_payment_method_id", "stripe_subscription_cancel_at_period_end",
  "stripe_subscription_canceled_at", "stripe_subscription_id", "stripe_subscription_period_end_at",
  "stripe_subscription_period_start_at", "stripe_subscription_status"
];

const replaceCommunityStripeSettings = async () => {
  const communitiesSettings = settings["communities"];
  const slugsToUpdate = Object.keys(communitiesSettings);

  // Update all communities not mentioned in the settings
  const updated = await prisma.membershipSetting.updateMany({
    where: {
      membership: {
        slug: {
          not: {
            in: slugsToUpdate
          }
        }
      }
    },
    data: communityStripeFields.reduce((acc, field) => {
      acc[field] = null;

      return acc;
    }, {})
  });

  console.log(`Removed Stripe settings for ${updated.count} communities`);

  for (slug of slugsToUpdate) {
    const communitySettings = communitiesSettings[slug];

    const membership = await prisma.membership.findUnique({
      where: {
        slug: slug
      },
      select: {
        id: true
      }
    });

    if (membership !== null) {
      await prisma.membershipSetting.update({
        where: {
          membership_id: membership.id
        },
        data: communityStripeFields.reduce((acc, field) => {
          acc[field] = communitySettings[field] !== undefined ? communitySettings[field] : null;

          return acc;
        }, {})
      });

      console.log(`Updated Stripe settings for Community "${slug}"`);
    } else {
      console.log(`Community "${slug}" not found`);
    }
  }
}

const replaceUserStripeSettings = async () => {
  const usersSettings = settings["users"];

  const idsToUpdate = Object.keys(usersSettings);

  // Update all users not mentioned in the settings
  const updated = await prisma.userProfile.updateMany({
    where: {
      user_id: {
        not: {
          in: idsToUpdate
        }
      }
    },
    data: userStripeFields.reduce((acc, field) => {
      acc[field] = null;

      return acc;
    }, {})
  });

  console.log(`Removed Stripe settings for ${updated.count} users`);

  for (id of idsToUpdate) {
    const userSettings = usersSettings[id];

    try {
      await prisma.userProfile.update({
        where: {
          user_id: id
        },
        data: userStripeFields.reduce((acc, field) => {
          acc[field] = userSettings[field] !== undefined ? userSettings[field] : null;

          return acc;
        }, {})
      });

      console.log(`Updated Stripe settings for User "${id}"`);
    } catch(e) {
      if (e.code === "P2025") {
        console.log(`User "${id}" not found`);
      } else {
        throw e;
      }
    }
  }
}

async function main() {
  if (NEXT_PUBLIC_ENV !== "staging") {
    console.log("This script can only be run in the staging environment");
    process.exit(-1);
  }

  if (!ACTIONS_REPLACE_STRIPE_SETTINGS) {
    console.log("ACTIONS_REPLACE_STRIPE_SETTINGS is not defined. No Stripe settings will be updated.");
    process.exit();
  }

  settings = JSON.parse(ACTIONS_REPLACE_STRIPE_SETTINGS);

  console.log(
    `Replacing Stripe settings for communities on environment ${NEXT_PUBLIC_ENV}\n using settings from the `
  );

  if (process.argv[2] !== "--yes") {
      const answer = await askQuestion("Do you want to proceed? (y/n): ");

      if (answer.toLowerCase() !== "y") {
        console.log("Good bye.");
        return;
      }
  }

  if (settings.communities) {
    await replaceCommunityStripeSettings();
  }

  if (settings.users) {
    await replaceUserStripeSettings();
  }
}

main()
