// Updates the host field on the memberships database, so that
// they are subdomains of the memberup-staging.com domain
//
// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/update-memberships-table-for-staging.js

require('dotenv').config()

const {PrismaClient} = require('@prisma/client')
const prisma = new PrismaClient()

const NEXT_PUBLIC_ENV = process.env.NEXT_PUBLIC_ENV

const updateMembershipsTableForStaging = async () => {
    if (NEXT_PUBLIC_ENV !== 'staging') {
        process.exit(-1)
    }

    try {
        await prisma.membershipSetting.updateMany({
            data: {
                custom_host_verified: null
            }
        })

        await prisma.$queryRaw`UPDATE memberships
            SET host = REPLACE(REPLACE(host, 'memberup-dev.com', 'memberup-staging.com'), 'memberup.com', 'memberup-staging.com');`
    } catch (e) {
        console.error(e)
        process.exit(1)
    }
}

updateMembershipsTableForStaging()
