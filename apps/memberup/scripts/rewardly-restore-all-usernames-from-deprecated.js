const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const prisma = new PrismaClient();

const REWARDFUL_API_KEY = process.env.REWARDFUL_API_KEY;
const REWARDFUL_ENDPOINT = 'https://api.getrewardful.com/v1/affiliates';
const USERS_FILE_PATH = path.join(__dirname, 'rewardfulUsersReverseDeprecated.json');

const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const fetchRewardfulUsers = async () => {
  let page = 1;
  let users = [];
  while (true) {
    console.log('Fetching page from Rewardful API:', page)
    const response = await axios.get(`${REWARDFUL_ENDPOINT}?page=${page}}&limit=100`, {
      headers: { Authorization: `Bearer ${REWARDFUL_API_KEY}` },
    });
    const rewardfulUsers = response.data.data
    rewardfulUsers.forEach(user => {
      users.push({ stripe_customer_id: user.stripe_customer_id, id: user.id, email: user.email}); // Use Rewardful user ID
    });
    if (rewardfulUsers.length === 0) break;
    page++;
    await delay(1000);
  }
  await fs.writeFile(USERS_FILE_PATH, JSON.stringify(users, null, 2));
  return users;
};

const undeprecateEmail = async (affiliateId, affiliateEmail) => {
  const cleanEmail = (email) => {
    return email.replace(/deprecated_\d+_(.*)/, '$1');
  };

  const affiliateEmailWithoutDeprecatedPrefix = cleanEmail(affiliateEmail)
  const data = {
    email: `${affiliateEmailWithoutDeprecatedPrefix}`,
    }
    
    console.log('updating', affiliateEmail, 'to', data.email)
    await axios.put(`https://api.getrewardful.com/v1/affiliates/${affiliateId}`, data, {
      auth: {
        username: REWARDFUL_API_KEY,
        password: '',
      },
    })
};



const processUsers = async () => {
  let users;
  try {
    await fs.access(USERS_FILE_PATH);
    console.log('Users file found. Reading users from file...');
    users = JSON.parse(await fs.readFile(USERS_FILE_PATH, 'utf8'));
  } catch (error) {
    console.log('Users file not found. Fetching users from Rewardful API...');
    users = await fetchRewardfulUsers();
  }

  let processedCount = 0;

  for (const user of users) {
      console.log(`Updating email to undeprecated for user with Rewardful ID ${user.id}...`);
      try{
      if(user.email.includes('deprecated_')){
         await undeprecateEmail(user.id, user.email);
      }
      console.log(`Updated email to undeprecated for user with Rewardful ID ${user.id}`);
     } catch (error) {
        console.log('Failed to update email for', user.email, error.data)
      }
      processedCount++;
  }

  console.log(`Processing completed. Total deleted users: ${processedCount}`);
};

processUsers().catch(console.error);
/*

REWARDFUL_API_KEY=your_production_api_key doppler run -- node apps/memberup/scripts/rewardly-restore-all-usernames-from-deprecated.js

*/