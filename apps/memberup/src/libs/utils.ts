import { NextApiRequest } from 'next'

import { IMembership, IMembershipSetting } from '@/shared-types/interfaces'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN

export const getCommunityBaseURL = (membership: Partial<IMembership>) => {
  if (!membership) return null

  return `/${membership.slug}`
}

export const getAbsoluteCommunityURL = (request: NextApiRequest, membership: Partial<IMembership>) => {
  if (!membership) return null

  const protocol = (request.headers['x-forwarded-proto'] as string) || 'http'
  const host = request.headers['host']
  return `${protocol}://${host}/${membership.slug}`
}

export const formatPrice = (priceObject) => {
  if (!priceObject || typeof priceObject.unit_amount !== 'number' || !priceObject.currency) {
    return null
  }
  const amount = (priceObject.unit_amount / 100).toFixed(2) // Convert cents to dollars and format to 2 decimals
  const currencySymbol = priceObject.currency.toUpperCase() === 'USD' ? '$' : priceObject.currency.toUpperCase() // Use $ for USD
  const type = priceObject.type === 'one_time' ? ' one time' : `/${priceObject.recurring.interval}`

  return `${currencySymbol}${amount}${type}`
}

export const extractPriceOrDefault = (data, targetPriceId) => {
  console.log('targetPriceId', targetPriceId, data)

  if (!data) return null

  if (targetPriceId) {
    const result = data.find((p) => p.id === targetPriceId)
    if (result) {
      return result
    }
  }

  for (const item of data) {
    if (item.product && item.product.default_price) {
      return item.product.default_price
    }
  }
  return null // Return null if no default price is found
}

export const getCommunityURL = (membership: any, path: string) => {
  return `${getCommunityBaseURL(membership)}${path.replace('/[slug]', '')}`
}

export function isCommunitySpace(space) {
  return space.name.toLowerCase() === 'community'
}
export function findCommunityChannel(spaces) {
  return spaces.docs.find((s) => s.name.toLowerCase() === 'community')
}

export function checkIsPaidMembership(membershipSettings: Partial<IMembershipSetting>) {
  return (
    membershipSettings?.stripe_prices?.some((price) => price.active) &&
    membershipSettings.stripe_connect_account?.stripe_user_id
  )
}

export const isUUIDv4 = (uuid) => {
  // Regular expression pattern for UUID v4
  const uuidv4Pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i

  // Test if the provided string matches the UUID v4 pattern
  return uuidv4Pattern.test(uuid)
}

export function moveArrayItem(arr: string[], oldIndex: number, newIndex: number) {
  if (newIndex >= arr.length) {
    let k = newIndex - arr.length + 1
    while (k--) {
      arr.push(undefined)
    }
  }
  arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0])
  return arr // For chainable calls, not needed if you want to modify the original array
}

export const EMOJI_REGEX =
  /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu

export const isEmoji = (input: string) => {
  return input.match(EMOJI_REGEX)
}

export const unescapeSlashes = (htmlString) => htmlString?.replace(/\\\//g, '/') || ''

export const unescapeSlashesAndAddLinks = (htmlString, primaryColor?) => {
  // Replace \/ with /
  const replacedString = unescapeSlashes(htmlString)
  // Regular expression to match URLs in a string
  const urlRegex = /(https?:\/\/[^\s<]+)(?![^<>]*>)/g
  // Replace URLs with anchor tags
  const anchorReplacedString = replacedString.replace(
    urlRegex,
    `<a href="$1" data-type="link" data-preview="false" target="_blank" style="${
      primaryColor ? `color:${primaryColor}` : ''
    }">$1</a>`,
  )
  return anchorReplacedString
}

export const transformMessage = (message) => {
  message = JSON.parse(JSON.stringify(message))
  message.reaction_counts = message.reaction_counts || {}
  message.reaction_counts['like'] = message.reaction_counts?.['like'] || 0

  function containsHTML(str) {
    const htmlRegex = /<\/?[a-z][\s\S]*>/i
    return htmlRegex.test(str)
  }

  if (!containsHTML(message.text)) {
    message.text = unescapeSlashesAndAddLinks(message.html)
  }

  if (isUUIDv4(message.title)) {
    message.title = null
  }

  return message
}

export const getFileTypeIcon = (mimetype, filename) => {
  const MEDIA_FILES_ICONS: any = {
    pdf: 'pdf.png',
    image: 'image.png',
    video: 'video.png',
    audio: 'audio.png',
    '.csv': 'csv.png',
    '.xls': 'excel.png',
    '.xlsx': 'excel.png',
    '.ppt': 'powerpoint.png',
    '.pptx': 'powerpoint.png',
    '.doc': 'word.png',
    '.docx': 'word.png',
    '.zip': 'zip.png',
  }
  const iconSrc =
    MEDIA_FILES_ICONS[mimetype?.toLowerCase()] ||
    MEDIA_FILES_ICONS[filename?.substring(filename.lastIndexOf('.'))] ||
    'other.png'
  return iconSrc
}

export class TimeSinceLastCall {
  private resetTimestamp: number
  private previousTimestamp: number
  constructor() {
    this.reset() // Initialize the previous timestamp and the resetTimestamp
  }

  _getCurrentTimestamp() {
    // Returns the current timestamp in milliseconds
    return new Date().getTime()
  }

  printTimeSinceLastCall(message = '') {
    // Get the current timestamp
    const currentTimestamp = this._getCurrentTimestamp()

    // Calculate the difference in seconds
    const secondsDifference = (currentTimestamp - this.previousTimestamp) / 1000

    // Print the message (if provided) along with the time difference in decimal format
    console.log(`${message} ${secondsDifference.toFixed(2)} seconds`)

    // Update the previous timestamp for the next call
    this.previousTimestamp = currentTimestamp
  }

  totalFromReset(message = '') {
    // Get the current timestamp
    const currentTimestamp = this._getCurrentTimestamp()

    // Calculate the difference in seconds from the last reset
    const secondsDifference = (currentTimestamp - this.resetTimestamp) / 1000

    // Print the message (if provided) along with the time difference in decimal format
    console.log(`${message} ${secondsDifference.toFixed(2)} seconds`)
  }

  reset() {
    // Reset the previous timestamp and the resetTimestamp to the current time
    this.previousTimestamp = this._getCurrentTimestamp()
    this.resetTimestamp = this._getCurrentTimestamp()
  }
}

export const getLocationInfo = () => {
  const isLocalhost = location.hostname.includes('localhost')
  const isNgrok = location.hostname.includes('ngrok.io')

  return {
    defaultDomain: DEFAULT_DOMAIN,
    domain: isLocalhost ? 'localhost' : isNgrok ? 'ngrok.io' : DEFAULT_DOMAIN,
    host: location.host,
    hostname: location.hostname,
    origin: location.origin,
    protocol: location.protocol,
    port: location.port,
    isLocalhost,
    isNgrok,
  }
}

export const isUrlValid = (str) => {
  const pattern = new RegExp(
    '^https?:\\/\\/' + // start of string and protocol
      '(www\\.)?' + // optional www
      '[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.' + // domain name
      '[a-zA-Z0-9()]{1,6}\\b' + // top-level domain
      '([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)', // rest of the URL
    'i',
  )
  return pattern.test(str)
}

export const roundCourseProgress = (progress) => {
  /* if it's less than 99% it should round up, if not  if should round down */
  if (!progress) return 0
  if (progress < 99) {
    return Math.ceil(progress)
  } else {
    return Math.floor(progress)
  }
}
