import { getFullName } from '@memberup/shared/src/libs/profile'
import { MENTION_MARKUP_EVERYONE } from '@memberup/shared/src/types/consts'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { IUser } from '@memberup/shared/src/types/interfaces'
import {
  ContentState,
  EditorState,
  Modifier,
  SelectionState,
  convertFromRaw,
  convertToRaw,
} from 'draft-js'

export const mentionGetEditorMentions = (editorCurrentContent) => {
  // gets the mention instances from the editor text using the entityMap the editor exposes which are user objects for each mention
  const contentState = convertToRaw(editorCurrentContent)
  const entityMap = contentState.entityMap
  const mentions = []
  Object.keys(entityMap).forEach((key) => {
    if (entityMap[key].type === 'mention') {
      const item = entityMap[key]
      const userData = item?.data?.mention
      mentions.push(userData)
    }
  })
  return mentions
}

export const isEditorEmpty = (editorState) => {
  if (!editorState) return true
  const contentState = editorState.getCurrentContent()
  return !contentState.hasText() && contentState.getBlockMap().size === 1
}

export function isMention(input) {
  //eslint-disable-next-line
  const regex = /\{\{mention:(user:[a-f0-9\-]{36}|all)\}\}/
  return regex.test(input)
}

export function mentionExtractUserIdFromMarkup(text: string) {
  if (text.startsWith('{{mention:user:') && text.endsWith('}}')) {
    const parts = text.split(':')
    const userId = parts[2].slice(0, -2) // Removing the last two characters "}}"
    return userId
  } else if (text.startsWith('{{mention:all}}')) {
    return 'all'
  }
  return null
}

export const mentionRefineContentText = (
  text: string,
  startsWith: string[],
  endsWith: string,
  suggestions
) => {
  const result = []
  const lengthOfText = text.length
  let position = 0

  while (position < lengthOfText) {
    let minStartIndex = lengthOfText
    let currentStartWith = ''

    startsWith.forEach((tag) => {
      const thisStartIndex = text.indexOf(tag, position)
      if (thisStartIndex > -1 && thisStartIndex < minStartIndex) {
        minStartIndex = thisStartIndex
        currentStartWith = tag
      }
    })

    if (currentStartWith) {
      const endIndex = text.indexOf(endsWith, minStartIndex)
      if (endIndex > 0) {
        const markup = text.slice(minStartIndex, endIndex + endsWith.length)
        const suggestion = suggestions.find((item) => item.markup === markup)

        if (suggestion) {
          result.push({
            data: {
              mention: suggestion,
            },
            startIndex: Array.from(text.slice(0, minStartIndex)).length,
          })
          text = text.replace(markup, suggestion.name)
          position = minStartIndex + suggestion.name.length
        } else {
          position = endIndex + endsWith.length
        }
      } else {
        position = lengthOfText
      }
    } else {
      position = lengthOfText
    }
  }
  return {
    text,
    mentions: result,
  }
}

export const mentionCreateEditorState = (text: string, suggestions: any) => {
  if (!text) return EditorState.createEmpty()

  let refineContentText = mentionRefineContentText(
    text || '',
    ['{{mention:user:', '{{mention:all}'],
    '}}',
    suggestions
  )
  const rawContent = convertToRaw(ContentState.createFromText(refineContentText.text))
  rawContent.entityMap = refineContentText.mentions.map((item) => ({
    type: 'mention',
    mutability: 'SEGMENTED',
    data: item.data,
  }))

  rawContent.blocks = rawContent.blocks.map((block) => {
    const ranges = []
    refineContentText.mentions.forEach((mention, index) => {
      const name = mention.data.mention.name
      const startIndex = block.text.indexOf(name, mention.startIndex)
      if (startIndex > -1) {
        ranges.push({
          key: index,
          length: name.length,
          offset: Array.from(block.text.slice(0, startIndex)).length,
        })
      }
    })
    return { ...block, entityRanges: ranges }
  })
  return EditorState.createWithContent(convertFromRaw(rawContent))
}

export const mentionGetMemberIdsIfEverbodyMentioned = (
  mentionedUsers: IUser[],
  currentUser: IUser
) => {
  const notifyAll = mentionedUsers
    .filter((m) => m)
    .some((el) => el.name.toLocaleLowerCase() === 'everyone')

  if (
    !notifyAll ||
    ![USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner, USER_ROLE_ENUM.creator].includes(
      currentUser.role as any
    )
  ) {
    return null
  }

  return true
}

export const mentionReplaceMentionsWithMarkup = (editorState) => {
  const editorContent = editorState.getCurrentContent()
  const plainText = editorContent.getPlainText()
  const mentions = mentionGetEditorMentions(editorContent)
  const newPlainText = mentions.reduce((acc, mention) => {
    const { name, markup } = mention
    const regex = new RegExp(name, 'g')
    return acc.replace(regex, markup)
  }, plainText)

  return newPlainText || ''
}

export const mentionGetSuggestions = (members, theme: 'light' | 'dark', user) => {
  const suggestions = Object.keys(members).map((key) => {
    const member = members[key]
    const name = member.name || getFullName(member.first_name, member.last_name, '')
    return {
      id: member.id,
      name,
      markup: `{{mention:user:${member.id}}}`,
      image: member?.profile?.image || member?.image,
      image_crop_area: member?.profile?.image_crop_area || member?.image_crop_area,
    }
  })

  if ([USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner, USER_ROLE_ENUM.creator].includes(user.role)) {
    const all = {
      id: '-1',
      name: 'Everyone',
      markup: MENTION_MARKUP_EVERYONE,
      letteredAvatarUrl: null,
    }

    suggestions.push(all as any)
  }
  return suggestions
}

export const insertEmoji = (e, editorState, onChangeEditorState) => {
  const selection = editorState.getSelection()
  const contentState = editorState.getCurrentContent()
  const newContentState = Modifier.insertText(contentState, selection, e.native)

  // Focus the cursor at the end of the content
  const blockMap = newContentState.getBlockMap()
  const lastBlock = blockMap.last()
  const lastKey = lastBlock.getKey()
  const length = lastBlock.getLength()
  const newSelectionState = new SelectionState({
    anchorKey: lastKey,
    anchorOffset: length,
    focusKey: lastKey,
    focusOffset: length,
  })

  const newEditorState = EditorState.push(editorState, newContentState, 'insert-characters')

  // Move the focus to the end of the content
  const editorStateWithCursorAtEnd = EditorState.forceSelection(newEditorState, newSelectionState)

  onChangeEditorState(editorStateWithCursorAtEnd)
}
