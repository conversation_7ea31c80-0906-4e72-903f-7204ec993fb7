import { RootState } from '../store'
import { PayloadAction, createSlice } from '@reduxjs/toolkit'

type DialogPayload = {
  dialog:
    | 'CreatePostReminder'
    | 'EditEvent'
    | 'EditLibraryContent'
    | 'EditPost'
    | 'EditSpace'
    | 'EditStream'
    | 'EditStreamInfo'
    | 'EditUserProfile'
    | 'LiveVideoSetup'
    | 'ManageAccount'
    | 'Notifications'
    | 'Search'
    | 'UserProfile'
    | 'WarningEditingContent'
    | 'CustomizeContentHeader'
    | 'UpgradePlanSpaceLimit'
    | 'MemberProfile'
    | 'MemberSettings'
  open: boolean
  props: any
}
export interface UIState {
  openDialogs: {
    [dialog: string]: {
      open: boolean
      props: any
    }
  }
  visibleTopSectionInHomePage: boolean
  visibleVideoSectionInGettingStart: boolean
  visibleTaskSectionInGettingStart: boolean
  markReadNotification: string
  refresh: boolean
  searchStatus: {
    member: boolean | null
    feed: boolean | null
    library: boolean | null
  }
  optimisticKnockUnseen: boolean
}

const initialState: UIState = {
  openDialogs: {},
  visibleTopSectionInHomePage: true,
  visibleVideoSectionInGettingStart: true,
  visibleTaskSectionInGettingStart: true,
  markReadNotification: null,
  refresh: false,
  searchStatus: {
    member: false,
    feed: false,
    library: false,
  },
  optimisticKnockUnseen: false,
}

export const uiSlice = createSlice({
  name: 'uiSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    openDialog: (state, action: PayloadAction<DialogPayload>) => {
      if (action.payload.open) {
        state.openDialogs[action.payload.dialog] = {
          open: action.payload.open,
          props: action.payload.props,
        }
      } else {
        delete state.openDialogs[action.payload.dialog]
      }
    },
    resetDialogs: (state) => {
      state.openDialogs = {}
    },
    setMarkReadNotification: (state, action: PayloadAction<string>) => {
      state.markReadNotification = action.payload
    },
    setVisibleSection: (state, action: PayloadAction<{ target: string; visible: boolean }>) => {
      state[action.payload.target] = action.payload.visible
    },
    setUIRefresh: (state) => {
      state.refresh = !state.refresh
    },
    setSearchStatus: (
      state,
      action: PayloadAction<{ searchIndex: string; hasResults: boolean }>
    ) => {
      state.searchStatus[action.payload.searchIndex] = action.payload.hasResults
    },
    setOptimisticKnockUnseen: (state, action: PayloadAction<boolean>) => {
      state.optimisticKnockUnseen = action.payload
    },
  },
})

export const {
  openDialog,
  resetDialogs,
  setVisibleSection,
  setMarkReadNotification,
  setUIRefresh,
  setSearchStatus,
  setOptimisticKnockUnseen,
} = uiSlice.actions
export const selectOpenDialogs = (state: RootState) => state.ui.openDialogs
export const selectMarkReadNotification = (state: RootState) => state.ui.markReadNotification
export const selectVisibleTopSectionInHomePage = (state: RootState) =>
  state.ui.visibleTopSectionInHomePage
export const selectVisibleVideoSectionInGettingStart = (state: RootState) =>
  state.ui.visibleVideoSectionInGettingStart
export const selectVisibleTaskSectionInGettingStart = (state: RootState) =>
  state.ui.visibleTaskSectionInGettingStart
export const selectUIRefresh = (state: RootState) => state.ui.refresh
export const selectSearchStatus = (state: RootState) => state.ui.searchStatus
export const selectOptimisticKnockUnseen = (state: RootState) => state.ui.optimisticKnockUnseen
export default uiSlice.reducer
