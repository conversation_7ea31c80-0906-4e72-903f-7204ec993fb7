import { PayloadAction, createSlice } from '@reduxjs/toolkit'

export interface FeedState {
  feedTrackIds: { [key: string]: number } | {}
}

const initialState: FeedState = {
  feedTrackIds: null,
}

export const feedTrackSlice = createSlice({
  name: 'feedTrackSlice',
  initialState,
  reducers: {
    setFeedTrackIds: (state, action: PayloadAction<{ [key: string]: number } | {}>) => {
      state.feedTrackIds = action.payload
    },
    addViewedFeed: (state, action: PayloadAction<{ id: string; updatedAt: number }>) => {
      state.feedTrackIds[action.payload.id] = action.payload.updatedAt
    },
  },
})

export const { setFeedTrackIds, addViewedFeed } = feedTrackSlice.actions

export default feedTrackSlice.reducer
