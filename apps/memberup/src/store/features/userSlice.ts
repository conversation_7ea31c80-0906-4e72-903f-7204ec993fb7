import { RootState } from '../store'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { IUser, IUserProfile } from '@memberup/shared/src/types/interfaces'
import { TError, TStartMembershipPayload } from '@memberup/shared/src/types/types'
import { PayloadAction, createSlice, current } from '@reduxjs/toolkit'

export interface UserState {
  canceledSignUp: boolean
  completeProfileStep: number
  user: IUser | null
  profile: IUserProfile | null
  knockUserToken: string
  streamChatUserToken: string
  sparkStreak: number
  requestGetActiveUser: boolean
  requestStartMembership: boolean
  requestUpdateProfile: boolean
  requestConnectStripeAccount: boolean
  requestDisconnectStripeAccount: boolean
  error: TError
  streamChatConnected: boolean
}

const initialState: UserState = {
  canceledSignUp: false,
  completeProfileStep: 0,
  user: null,
  profile: null,
  knockUserToken: '',
  streamChatUserToken: '',
  sparkStreak: 0,
  requestGetActiveUser: false,
  requestStartMembership: false,
  requestUpdateProfile: false,
  requestConnectStripeAccount: false,
  requestDisconnectStripeAccount: false,
  error: null,
  streamChatConnected: false,
}

export const userSlice = createSlice({
  name: 'userSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    resetUser: (state, action: PayloadAction<Partial<UserState>>) => {
      let keys = Object.keys(action.payload)
      let data

      if (keys.length) {
        data = action.payload
      } else {
        keys = Object.keys(initialState)
        data = initialState
      }
      for (const key of keys) {
        state[key] = data[key]
      }
    },
    setStreamChatConnected: (state, action: PayloadAction<boolean>) => {
      state.streamChatConnected = action.payload
    },
    setCanceledSignUp: (state, action: PayloadAction<boolean>) => {
      state.error = null
      state.canceledSignUp = action.payload
    },
    setCompleteUserProfileStep: (state, action: PayloadAction<number>) => {
      state.error = null
      state.completeProfileStep = action.payload
    },
    getActiveUser: (state, action: PayloadAction<{ time_zone?: string }>) => {
      state.error = null
      state.user = null
      state.profile = null
      state.requestGetActiveUser = true
    },
    getActiveUserSuccess: (
      state,
      action: PayloadAction<{
        user: IUser
        profile?: IUserProfile
        streamChatUserToken?: string
        knockToken?: string
      }>
    ) => {
      const before = current(state)
      const { user, profile, streamChatUserToken, knockToken } = action.payload

      if (profile) {
        state.profile = profile
      }
      if (knockToken) {
        state.knockUserToken = knockToken
      }
      if (streamChatUserToken) {
        state.streamChatUserToken = streamChatUserToken
      }

      state.user = {
        ...(before.user || {}),
        ...user,
      }
      state.error = null
      state.requestGetActiveUser = false
    },
    getActiveUserFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.profile = null
      state.requestGetActiveUser = false
    },
    getActiveUserProfileSuccess: (state, action: PayloadAction<{ profile: IUserProfile }>) => {
      state.error = null
      state.profile = action.payload.profile
    },
    startMembership: (state, action: PayloadAction<TStartMembershipPayload>) => {
      state.requestStartMembership = true
    },
    updateUserProfile: (
      state,
      action: PayloadAction<{
        data: Partial<IUserProfile>
        successMessage?: string
        errorMessage?: string
      }>
    ) => {
      state.requestUpdateProfile = true
      // const before = current(state.profile) || {}
      // const payload = action.payload as any
      // state.profile = {
      //   ...before,
      //   ...payload,
      // }
    },
    mergeUserProfile: (
      state,
      action: PayloadAction<{ spark_hidden_at?: any; pinned_posts_hidden?: any }>
    ) => {
      state.profile = { ...state.profile, ...action.payload }
    },
    updateUserProfileSuccess: (state, action: PayloadAction<IUserProfile>) => {
      const before = current(state)
      const { user, ...rest } = (action.payload || {}) as any
      let completedProfileStep = rest?.completed_profile ? 2 : 0
      completedProfileStep =
        completedProfileStep === 2 && rest?.completed_image ? 3 : completedProfileStep
      state.completeProfileStep = completedProfileStep
      if (user) {
        state.user = {
          ...(before.user || {}),
          ...user,
        }
      }
      state.profile = {
        ...(before.profile || {}),
        ...(rest || {}),
      }

      state.requestUpdateProfile = false
    },
    updateUserProfileFailure: (state, action: PayloadAction<TError>) => {
      state.requestUpdateProfile = false
    },
    setUserError: (state, action: PayloadAction<TError>) => {
      state.profile = null
      state.error = action.payload
      state.requestGetActiveUser = false
      state.requestUpdateProfile = false
    },
  },
})

export const {
  startMembership,
  getActiveUser,
  getActiveUserSuccess,
  getActiveUserFailure,
  getActiveUserProfileSuccess,
  updateUserProfile,
  mergeUserProfile,
  updateUserProfileSuccess,
  updateUserProfileFailure,
  resetUser,
  setCanceledSignUp,
  setCompleteUserProfileStep,
  setUserError,
  setStreamChatConnected,
} = userSlice.actions

export const selectStreamChatConnected = (state: RootState) => state.user.streamChatConnected

export const selectCanceledSignUp = (state: RootState) => state.user.canceledSignUp
export const selectCompleteUserProfileStep = (state: RootState) => state.user.completeProfileStep
export const selectUser = (state: RootState) => state.user.user
export const selectUserFullName = (state: RootState) =>
  getFullName(state.user.user?.first_name, state.user.user?.last_name, '')
export const selectUserProfile = (state: RootState) => state.user.profile
export const selectUserMembershipPlan = (state: RootState) => state.user.plan
export const selectLibraryLayout = (state: RootState) => state.user.profile.library_layout
export const selectKnockToken = (state: RootState) => state.user.knockUserToken
export const selectStreamChatUserToken = (state: RootState) => state.user.streamChatUserToken
export const selectUserSparkStreak = (state: RootState) => state.user.sparkStreak
export const selectRequestConnectStripeAccount = (state: RootState) =>
  state.user.requestConnectStripeAccount
export const selectRequestGetActiveUser = (state: RootState) => state.user.requestGetActiveUser
export const selectRequestUpdateProfile = (state: RootState) => state.user.requestUpdateProfile
export const selectRequestStartMembership = (state: RootState) => state.user.requestStartMembership
export const selectUserError = (state: RootState) => state.user.error
export default userSlice.reducer
