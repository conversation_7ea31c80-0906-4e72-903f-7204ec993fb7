import { createSlice, current, PayloadAction } from '@reduxjs/toolkit'

import { RootState } from '../store'
import { ILive, ILiveStream } from '@memberup/shared/src/types/interfaces'
import { TError } from '@memberup/shared/src/types/types'

export interface LibraryState {
  newLiveStream: ILiveStream
  activeLive: ILive
  requestGetLive: boolean
  requestCreateLive: boolean
  requestUpdateLive: boolean
  requestCreateLiveStream: boolean
  requestDeleteLiveStream: boolean
  error: TError
}

const initialState: LibraryState = {
  newLiveStream: null,
  activeLive: null,
  requestGetLive: false,
  requestCreateLive: false,
  requestUpdateLive: false,
  requestCreateLiveStream: false,
  requestDeleteLiveStream: false,
  error: null,
}

export const liveSlice = createSlice({
  name: 'liveSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    getLive: (state, action: PayloadAction<{ id?: string; stream_id?: string }>) => {
      state.error = null
      state.activeLive = null
      state.requestGetLive = true
    },
    getLiveSuccess: (state, action: PayloadAction<ILive>) => {
      state.activeLive = action.payload
      state.requestGetLive = false
    },
    getLiveFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestGetLive = false
    },
    createLive: (state, action: PayloadAction<ILive>) => {
      state.error = null
      state.requestCreateLive = true
    },
    createLiveSuccess: (state, action: PayloadAction<ILive>) => {
      const before = current(state)
      state.activeLive = {
        ...action.payload,
        live_stream_token: before.activeLive?.live_stream_token,
      }
      state.requestCreateLive = false
    },
    createLiveFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestCreateLive = false
    },
    updateLive: (state, action: PayloadAction<{ id?: string; data?: ILive }>) => {
      state.error = null
      state.requestUpdateLive = true
    },
    updateLiveSuccess: (state, action: PayloadAction<ILive>) => {
      const before = current(state)
      state.activeLive = {
        ...action.payload,
        live_stream_token: before.activeLive?.live_stream_token,
      }
      state.requestUpdateLive = false
    },
    updateLiveFailure: (state, action: PayloadAction<TError>) => {
      state.error = action.payload
      state.requestUpdateLive = false
    },
    deleteLiveStream: (state, action: PayloadAction<{ stream_id: string }>) => {
      state.error = null
      state.activeLive = null
      state.requestDeleteLiveStream = true
    },
  },
})

export const {
  getLive,
  getLiveSuccess,
  getLiveFailure,
  createLive,
  createLiveSuccess,
  createLiveFailure,
  updateLive,
  updateLiveSuccess,
  updateLiveFailure,
  deleteLiveStream,
} = liveSlice.actions
export const selectActiveLive = (state: RootState) => state.live.activeLive
export const selectRequestGetLive = (state: RootState) => state.live.requestGetLive
export const selectRequestCreateLive = (state: RootState) => state.live.requestCreateLive
export const selectRequestUpdateLive = (state: RootState) => state.live.requestUpdateLive
export const selectRequestCreateLiveStream = (state: RootState) => state.live.requestCreateLiveStream
export default liveSlice.reducer
