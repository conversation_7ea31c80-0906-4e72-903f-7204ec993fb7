import { spaceSlice } from '@/memberup/store/features/spaceSlice'
import { RootState } from '@/memberup/store/store'
import { SPACE_TYPE_ENUM } from '@/shared-types/enum'
import { createSelector, createSlice, current, PayloadAction } from '@reduxjs/toolkit'

const addSpaceImplementation = (state: any, payload: any) => {
  const { id, name, slug, spaceType } = payload
  state.spaces[id] = {
    id: id,
    name: name,
    slug: slug,
    spaceType: spaceType,
    messages: [],
    orderBy: 'activity',
    next: undefined,
  }
}

// Initial state
const initialState: {
  currentSpace: string | null
  spaces: {
    [key: string]: {
      name: string
      slug: string
      messages: any[]
      pinnedMessages: any[]
      orderBy: 'activity' | 'newest'
      next: string | undefined
      id: string
      spaceType: SPACE_TYPE_ENUM
    }
  }
} = {
  currentSpace: null,
  spaces: {},
}

// Slice
const feedAggregationSlice = createSlice({
  name: 'feedAggregationSlice',
  initialState,
  reducers: {
    resetState: (state) => {
      Object.assign(state, initialState)
    },
    initializeSpaces: (
      state,
      action: PayloadAction<{
        spaces: {
          id: string
          name: string
          slug: string
          isHome?: boolean
          orderBy?: string
          spaceType: SPACE_TYPE_ENUM
        }[]
        default: string
      }>
    ) => {
      action.payload.spaces.forEach((space) => {
        const id = space.id
        state.spaces[id] = {
          name: space.name,
          spaceType: space.spaceType,
          messages: [],
          pinnedMessages: [],
          slug: space.slug,
          orderBy: 'activity',
          id: id,
          next: undefined,
        }
      })
    },
    removeSpace: (state, action: PayloadAction<string>) => {
      delete state.spaces[action.payload]
    },
    addSpace: (
      state: any,
      action: PayloadAction<{ id: string; name: string; slug: string; spaceType: SPACE_TYPE_ENUM }>
    ) => {
      const { id, name, slug, spaceType } = action.payload
    },
    updateSpace: (
      state,
      action: PayloadAction<{
        id: string
        name?: string
        orderBy?: 'newest' | 'activity'
      }>
    ) => {
      const { id, name, orderBy } = action.payload
      const existingSpace = state.spaces[id]
      state.spaces[id] = {
        ...existingSpace,
        name: name || existingSpace.name,
        orderBy: orderBy || existingSpace.orderBy,
      }
    },
    setCurrentSpace: (state, action: PayloadAction<string>) => {
      state.currentSpace = action.payload
    },
    setMessages: (
      state,
      action: PayloadAction<{
        id: string
        messages: string[]
        next?: string
        mode: 'replace' | 'append'
      }>
    ) => {
      const { id, messages, next, mode } = action.payload

      if (state.spaces[id]) {
        if (mode === 'replace') {
          state.spaces[id].messages.length = 0
          state.spaces[id].messages.push(...messages)
          state.spaces[id].next = next
        } else {
          state.spaces[id].next = next
          state.spaces[id].messages.push(...messages)
        }
      }
    },
    addMessage: (
      state,
      action: PayloadAction<{
        id: string
        message: any
      }>
    ) => {
      const { id, message } = action.payload
      const space = state.spaces[id]
      if (space) {
        //console.log('OLD MESSAGE LIST', current(space.messages))
        space.messages.unshift(message) // Add the new message at the start of the list
        //console.log('NEW MESSAGE LIST', current(space.messages))
      }
    },
    deleteMessage: (
      state,
      action: PayloadAction<{
        id: string
        message: any
      }>
    ) => {
      const { id, message } = action.payload
      const space = state.spaces[id]
      if (!space) {
        return
      }
      const index = space.messages.findIndex((msg: any) => msg.id === message.id)
      if (index !== -1) {
        space.messages.splice(index, 1)
      }

      // Remove from pinned messages
      if (id === 'community' && message.pinned) {
        const pinnedMessageIndex = state.spaces['community'].pinnedMessages.findIndex(
          (msg) => msg.id === message.id
        )
        if (pinnedMessageIndex !== -1) {
          state.spaces['community'].pinnedMessages.splice(pinnedMessageIndex, 1)
        }
      }
    },
    updateMessage: (
      state,
      action: PayloadAction<{
        id: string
        message: any
      }>
    ) => {
      const { id, message } = action.payload
      const space = state.spaces[id]
      if (!space) {
        return
      }
      // Reported or Pinned message will be removed from normal messages
      if (message.feed_status === 'reported' || message.pinned) {
        // Always try remove reported or pinned messages
        const index = space.messages.findIndex((msg: any) => msg.id === message.id)
        if (index !== -1) {
          space.messages.splice(index, 1)
        }

        // Update or add pinned message to community
        if (id === 'community' && message.pinned) {
          const existingIndex = state.spaces['community'].pinnedMessages.findIndex(
            (msg: any) => msg.id === message.id
          )
          if (existingIndex !== -1) {
            state.spaces['community'].pinnedMessages[existingIndex] = message
          } else {
            state.spaces['community'].pinnedMessages.unshift(message)
          }
        }
        return
      }
      // Potential upin action. If message is found on pinned messages we remove it.
      if (id === 'community') {
        const existingIndex = state.spaces['community'].pinnedMessages.findIndex(
          (msg: any) => msg.id === message.id
        )
        if (existingIndex !== -1) {
          space.pinnedMessages.splice(existingIndex, 1)
        }
      }

      if (space.orderBy === 'newest') {
        // Replace the old message with the updated message, the order does not change.
        const existingIndex = space.messages.findIndex((msg: any) => msg.id === message.id)
        if (existingIndex !== -1) {
          space.messages[existingIndex] = message
        } else {
          // Try to insert the message if is in between existing messages.
          if (space.messages.length === 0) {
            space.messages.unshift(message)
          } else {
            const insertIndex = space.messages.findIndex(
              (msg) => msg.created_at <= message.created_at
            )
            if (insertIndex !== -1) {
              space.messages.splice(insertIndex, 0, message)
            } else {
              space.messages.push(message)
            }
          }
        }
      } else if (space.orderBy === 'activity') {
        // If the activity timestamp has changed, remove the old message and add the updated message at the start of the list.
        const existingIndex = space.messages.findIndex((msg: any) => msg.id === message.id)
        if (existingIndex !== -1) {
          // Update the message if it was updated for other reason than for a new comment.
          if (space.messages[existingIndex].last_activity_at === message.last_activity_at) {
            space.messages[existingIndex] = message
          } else {
            // Remove from the old position and add at the first position.
            space.messages.splice(existingIndex, 1)
            space.messages.unshift(message)
          }
        } else {
          if (space.messages.length === 0) {
            space.messages.unshift(message)
          } else {
            const insertIndex = space.messages.findIndex(
              (msg) => msg.last_activity_at <= message.last_activity_at
            )
            if (insertIndex !== -1) {
              space.messages.splice(insertIndex, 0, message)
            } else {
              space.messages.push(message)
            }
          }
        }
      }
    },
    setPinnedMessages: (
      state,
      action: PayloadAction<{
        id: string
        messages: any[]
      }>
    ) => {
      const { id, messages } = action.payload
      if (state.spaces[id]) {
        state.spaces[id].pinnedMessages.length = 0
        state.spaces[id].pinnedMessages.push(...messages)
      }
    },
    addMessages: (
      state,
      action: PayloadAction<{
        id: string
        messages: any[]
        next?: string
      }>
    ) => {
      const { id, messages, next } = action.payload
      if (state.spaces[id]) {
        state.spaces[id].next = next
        state.spaces[id].messages.push(...messages)
      }
    },
    addMessageToTop: (state, action: PayloadAction<{ spaceId: string; message: string }>) => {
      const { spaceId, message } = action.payload
      if (state.spaces[spaceId] && state.spaces[spaceId].orderBy === 'activity') {
        state.spaces[spaceId].messages.unshift(message)
      }
    },
    setOrderBy: (
      state,
      action: PayloadAction<{ spaceId: string; orderBy: 'activity' | 'newest' }>
    ) => {
      const { spaceId, orderBy } = action.payload
      if (state.spaces[spaceId]) {
        state.spaces[spaceId].orderBy = orderBy
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(spaceSlice.actions.getChannelsSuccess, (state, action) => {
      state.spaces = action.payload.data.docs
        .filter((c) => c.active && c.visibility)
        .map((c) => ({
          id: c.id,
          name: c.name,
          slug: c.slug,
          messages: [],
          orderBy: 'activity',
          next: undefined,
          spaceType: c.space_type,
        }))
        .reduce((acc, obj) => {
          acc[obj.id] = obj
          return acc
        }, {})

      state.spaces['community'] = {
        id: 'community',
        name: 'Community',
        slug: 'community',
        messages: [],
        pinnedMessages: [],
        orderBy: 'activity',
        next: undefined,
        spaceType: SPACE_TYPE_ENUM.home,
      }
    })
    builder.addCase(spaceSlice.actions.upsertChannelSuccess, (state, action) => {
      if (action.payload.visibility) {
        addSpaceImplementation(state, {
          id: action.payload.id,
          name: action.payload.name,
          slug: action.payload.slug,
          spaceType: action.payload.space_type,
        })
      } else {
        delete state.spaces[action.payload.id]
      }
    })
    builder.addCase(spaceSlice.actions.deleteChannelSuccess, (state, action) => {
      delete state.spaces[action.payload]
    })
  },
})

export const {
  initializeSpaces,
  // setCurrentSpace,
  addMessages,
  setMessages,
  setPinnedMessages,
  // addMessageToTop,
  setOrderBy,
  //updateSpace,
  addMessage,
  deleteMessage,
  updateMessage,
  resetState,
} = feedAggregationSlice.actions

export const selectAllSpaceIds = (state: RootState) => Object.keys(state.feedAggregation.spaces)
export const selectSpaceBySlug = (state: RootState, spaceSlug: string) =>
  Object.values(state.feedAggregation.spaces).find(
    (space: any) => space.slug.toLowerCase() === spaceSlug
  )

export const selectSpaceInfoBySlug = (state: RootState, spaceSlug: string) => {
  const result: any = Object.values(state.feedAggregation.spaces).find(
    (space: any) => space.slug.toLowerCase() === spaceSlug
  )
  if (result) {
    return {
      id: result.id,
      slug: result.slug,
    }
  }
}

export const selectSpacesInfo = (state: RootState) => {
  return Object.values(state.feedAggregation.spaces).map((space: any) => {
    return {
      id: space.id,
      slug: space.slug,
    }
  })
}

export const selectPinnedPostsCount = (state: RootState) => {
  return state.feedAggregation.spaces['community']?.pinnedMessages?.length || 0
}

export const selectSpacesArray = createSelector(
  (state: RootState) => state.feedAggregation.spaces,
  (spaces) => Object.values(spaces)
)

export default feedAggregationSlice.reducer
