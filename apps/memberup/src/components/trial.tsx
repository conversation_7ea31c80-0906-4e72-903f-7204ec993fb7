import useTrial from '@/memberup/components/hooks/use-trial'
import InfoIcon from '@mui/icons-material/Info'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { useRouter } from 'next/router'
import moment from 'moment-timezone'

const AppTrial = () => {
  const { isTrial, isTrialExpired, trialEnd, remainingHoursForPayment } = useTrial()
  const formatTrialEndDate = (trialEnd) => {
    let date = moment.unix(trialEnd) // Replace with your date
    let formattedDate = date.format('MMMM Do, YYYY')
    return formattedDate
  }
  if (!isTrial) return null

  return (
    isTrialExpired && (
      <div className={'m-10 text-center text-red-500 font-bold text-xs'}>
        Attention: Your trial has ended on {formatTrialEndDate(trialEnd)}. If the payment is not
        processed within {remainingHoursForPayment}hs your community will be inactivated.
      </div>
    )
  )

  //
  // if (!isTrial || trialDays <= 0) return null
  //
  // return (
  //   <div className="flex-item" style={{ padding: 12, textAlign: 'center' }}>
  //     <Grid container spacing={1} alignItems="center">
  //       <Grid item xs></Grid>
  //       <Grid item>
  //         <InfoIcon color="primary" />
  //       </Grid>
  //       <Grid item>
  //         You have {trialDays} days remaining in your trial period. After this, your credit card
  //         will be automatically charged for the Infinite Plan.
  //       </Grid>
  //       <Grid item xs></Grid>
  //     </Grid>
  //   </div>
  // )
}

AppTrial.displayName = 'AppTrial'

export default AppTrial
