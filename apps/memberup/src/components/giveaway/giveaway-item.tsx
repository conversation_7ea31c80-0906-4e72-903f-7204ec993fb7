import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React from 'react'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    maxWidth: 170,
    margin: 'auto',
  },
  thumbnails: {
    width: '100%',
    maxWidth: 170,
    height: 170,
    backgroundColor: '#000000',
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 1,
    marginBottom: 8,
    overflow: 'hidden',
    '& img': {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
    },
  },
}))

const GiveawayItem: React.FC<{ giveaway: any }> = ({ giveaway }) => {
  const classes = useStyles()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <div className={classes.root}>
      <div className={clsx(classes.thumbnails, 'app-image-wrapper border-color02')}>
        <AppImg
          src={`${membershipAssetsPath}/images/giveaway.png`}
          height={100}
          width={100}
          alt="Gievaway Image"
        />
      </div>
      <Typography variant="subtitle1">{giveaway?.title || 'Private Event Ticket'}</Typography>
    </div>
  )
}

export default GiveawayItem
