import React, { useState, useCallback, useEffect } from 'react'
import Cropper from 'react-easy-crop'
import { Area, Point } from 'react-easy-crop/types'
import IconButton from '@mui/material/IconButton'
import ZoomInIcon from '@mui/icons-material/ZoomIn'
import ZoomOutIcon from '@mui/icons-material/ZoomOut'
import CloseIcon from '@mui/icons-material/Close'
import Slider from '@mui/material/Slider'
import { Box, Stack } from '@mui/material'
import DraggableBadge from './draggable-badge'
import useAppTheme from '../hooks/use-app-theme'

interface InteractiveImageCropperProps {
  image: string
  onImageCropped: (
    croppedImage: string,
    croppedAreaPixels: any,
    croppedArea: any,
    zoom: number
  ) => void
  onCancel: () => void
  aspectRatio?: number
  initialCroppedArea?: any
  showZoomControls?: boolean
  aspectRatioOverride?: number
  height?: string
  width?: string
  customizeAlignment?: string
}

export const InteractiveImageCropperCustomizeContent: React.FC<InteractiveImageCropperProps> = ({
  image,
  onImageCropped,
  aspectRatio = 2,
  onCancel = () => {},
  initialCroppedArea = null,
  aspectRatioOverride = null,
  height = '285px',
  width = '100%',
  customizeAlignment = 'left',
}) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(initialCroppedArea?.zoom ?? 1)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area>(null)
  const [croppedArea, setCroppedArea] = useState<Area>(null)
  const [aspectRatioState, _] = useState(aspectRatio)
  const { isDarkTheme, theme } = useAppTheme()

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels)
    setCroppedArea(croppedArea)
  }, [])

  useEffect(() => {
    if (image && croppedArea) {
      handleSave()
    }
  }, [image, croppedArea])

  const createImageForCropping = (url) =>
    new Promise<HTMLImageElement>((resolve, reject) => {
      const image = new Image()
      image.crossOrigin = 'anonymous'
      image.addEventListener('load', () => resolve(image))
      image.addEventListener('error', (error) => reject(error))
      image.src = url
    })

  const getCroppedImg = async (imageSrc, pixelCrop, rotation = 0) => {
    const image = await createImageForCropping(imageSrc)
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    const maxSize = Math.max(image.width, image.height)
    const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2))

    canvas.width = safeArea
    canvas.height = safeArea

    ctx.translate(safeArea / 2, safeArea / 2)
    ctx.rotate((rotation * Math.PI) / 180)
    ctx.translate(-safeArea / 2, -safeArea / 2)

    ctx.drawImage(image, safeArea / 2 - image.width * 0.5, safeArea / 2 - image.height * 0.5)
    const data = ctx.getImageData(0, 0, safeArea, safeArea)

    canvas.width = pixelCrop.width
    canvas.height = pixelCrop.height

    const dx = Math.round(0 - safeArea / 2 + image.width * 0.5 - pixelCrop.x)
    const dy = Math.round(0 - safeArea / 2 + image.height * 0.5 - pixelCrop.y)

    if (!dx || !dy) {
      return null
    }

    ctx.putImageData(data, dx, dy)
    return new Promise((resolve) => {
      canvas.toBlob((file) => {
        if (!file) {
          resolve(null)
        } else {
          resolve(URL.createObjectURL(file))
        }
      }, 'image/jpeg')
    })
  }

  const handleSave = async () => {
    if (image) {
      const croppedImage = await getCroppedImg(image, croppedAreaPixels)
      onImageCropped(croppedImage as any, croppedAreaPixels, croppedArea, zoom)
    }
  }

  return (
    <>
      <div
        style={{
          position: 'relative',
          width,
          height,
          overflow: 'hidden',
        }}
      >
        <Cropper
          image={image}
          crop={crop}
          aspect={aspectRatioOverride ?? aspectRatioState}
          onCropChange={setCrop}
          onCropComplete={onCropComplete}
          initialCroppedAreaPercentages={initialCroppedArea?.croppedArea}
          style={{
            containerStyle: {
              borderRadius: '16px',
              width: '100%',
            },
            cropAreaStyle: {
              borderRadius: 0,
              borderWidth: '0px',
              borderColor: 'transparent',
              margin: '0px',
              padding: '0px',
            },
          }}
          objectFit="horizontal-cover"
          showGrid={false}
        />

        <DraggableBadge
          style={{
            position: 'absolute',
            top: '80%',
            left: customizeAlignment === 'left' || customizeAlignment === 'center' ? '85%' : '15%',
            transform: 'translate(-50%, -50%)',
          }}
        />

        <IconButton
          style={{
            position: 'absolute',
            left: customizeAlignment === 'left' || customizeAlignment === 'center' ? '92%' : '2%',
            top: 10,
            zIndex: 100000,
            backgroundColor: isDarkTheme ? '#17171a' : '#fff',
            color: theme.palette.text.disabled,
          }}
          onClick={onCancel}
        >
          <CloseIcon />
        </IconButton>
      </div>
    </>
  )
}

const InteractiveImageCropper: React.FC<InteractiveImageCropperProps> = ({
  image,
  onImageCropped,
  aspectRatio = 2,
  onCancel = () => {},
  initialCroppedArea = null,
  showZoomControls = false,
  aspectRatioOverride = null,
  height = '285px',
  width = '100%',
}) => {
  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(initialCroppedArea?.zoom ?? 1)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area>(null)
  const [croppedArea, setCroppedArea] = useState<Area>(null)
  const [aspectRatioState, _] = useState(aspectRatio)

  const handleChangeZoom = (event: Event, newValue: number | number[]) => {
    setZoom((newValue as number) / 100)
  }

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels)
    setCroppedArea(croppedArea)
  }, [])

  useEffect(() => {
    if (image && croppedArea) {
      handleSave()
    }
  }, [image, croppedArea])

  const createImageForCropping = (url) =>
    new Promise<HTMLImageElement>((resolve, reject) => {
      const image = new Image()
      image.crossOrigin = 'anonymous'
      image.addEventListener('load', () => resolve(image))
      image.addEventListener('error', (error) => reject(error))
      image.src = url
    })

  const getCroppedImg = async (imageSrc, pixelCrop, rotation = 0) => {
    const image = await createImageForCropping(imageSrc)
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    const maxSize = Math.max(image.width, image.height)
    const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2))

    canvas.width = safeArea
    canvas.height = safeArea

    ctx.translate(safeArea / 2, safeArea / 2)
    ctx.rotate((rotation * Math.PI) / 180)
    ctx.translate(-safeArea / 2, -safeArea / 2)

    ctx.drawImage(image, safeArea / 2 - image.width * 0.5, safeArea / 2 - image.height * 0.5)
    const data = ctx.getImageData(0, 0, safeArea, safeArea)

    canvas.width = pixelCrop.width
    canvas.height = pixelCrop.height

    ctx.putImageData(
      data,
      Math.round(0 - safeArea / 2 + image.width * 0.5 - pixelCrop.x),
      Math.round(0 - safeArea / 2 + image.height * 0.5 - pixelCrop.y)
    )
    return new Promise((resolve) => {
      canvas.toBlob((file) => {
        resolve(URL.createObjectURL(file))
      }, 'image/jpeg')
    })
  }

  const handleSave = async () => {
    if (image) {
      const croppedImage = await getCroppedImg(image, croppedAreaPixels)
      onImageCropped(croppedImage as any, croppedAreaPixels, croppedArea, zoom)
    }
  }

  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.1, 3))
  }

  const handleZoomOut = () => {
    setZoom((prevZoom) => Math.max(prevZoom - 0.1, 1)) // Decrease the zoom level by 0.1 down to a minimum of 1
  }

  return (
    <>
      {showZoomControls && (
        <Box>
          <Stack spacing={2} direction="row" sx={{ mb: 1 }} alignItems="center">
            <IconButton onClick={handleZoomOut}>
              <ZoomOutIcon />
            </IconButton>
            <Slider
              aria-label="Zoom"
              min={100}
              max={300}
              value={zoom * 100}
              onChange={handleChangeZoom}
              step={2}
            />
            <IconButton onClick={handleZoomIn}>
              <ZoomInIcon />
            </IconButton>
          </Stack>
        </Box>
      )}
      <div style={{ position: 'relative', width, height, overflow: 'hidden' }}>
        <Cropper
          image={image}
          crop={crop}
          zoom={zoom}
          aspect={aspectRatioOverride ?? aspectRatioState}
          onCropChange={setCrop}
          onCropComplete={onCropComplete}
          onZoomChange={setZoom}
          initialCroppedAreaPercentages={initialCroppedArea?.croppedArea}
          style={{
            containerStyle: {
              borderRadius: '16px',
            },
            cropAreaStyle: {
              borderRadius: 0,
              borderWidth: '0px',
              borderColor: 'transparent',
            },
            mediaStyle: {
              opacity: 0.9,
            },
          }}
          showGrid={false}
          objectFit="horizontal-cover"
        />

        <DraggableBadge
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        />
      </div>
    </>
  )
}

export default InteractiveImageCropper
