import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'

import useTrial from '@/memberup/components/hooks/use-trial'

const AppTrialEnded = () => {
  const { isTrial, trialDays } = useTrial()

  if (!isTrial || trialDays === -1000 || trialDays > 0) return null

  return (
    <Box
      sx={{
        backgroundColor: '#BFB1E2',
        borderRadius: '12px',
        p: 4,
        width: '100%',
      }}
    >
      <Typography variant="h4" style={{ color: '#000000' }}>
        Uh Oh! Your discovery period has ended.
      </Typography>
      <br />
      <Typography variant="body1" style={{ color: '#000000' }}>
        Your work is safe and sound, but your account is now frozen, so you and your members can&rsquo;t access it. To
        pick up where you left off, just click the button below and upgrade now.
      </Typography>
      <br />
      <Button className="round-small" color="primary" variant="contained">
        Upgrade Now 🚀
      </Button>
    </Box>
  )
}

AppTrialEnded.displayName = 'AppTrialEnded'

export default AppTrialEnded
