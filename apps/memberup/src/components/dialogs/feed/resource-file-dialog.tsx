import * as React from 'react'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import Modal from '@mui/material/Modal'
import Box from '@mui/material/Box'
import FormControl from '@mui/material/FormControl'
import useTheme from '@mui/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { DialogTitle, FormLabel, LinearProgress } from '@mui/material'
import { useRef } from 'react'
import { SVGUpload } from '@memberup/shared/src/components/svgs/upload'
import { capitalizeAllWords } from '@/shared-libs/string-utils'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import useAppTheme from '../../hooks/use-app-theme'
import FileAttachments from '../../list/file-attachments'

export default function ResourceFileDialog({
  open,
  text: initialText,
  onCancel,
  onSave,
  subtitle,
  public_id,
  url,
  handleFileSelect,
  resourceUploadProgress,
  currentFile,
  deleteFile,
  initUploadResourceFiles,
  cancelResourceUploads,
}: {
  open: boolean
  text?: string
  url?: string
  onCancel?: () => void
  onSave: (currentFile: any) => void
  subtitle?: string
  public_id?: string
  handleFileSelect: any
  resourceUploadProgress: number
  currentFile: any
  deleteFile: any
  initUploadResourceFiles: any
  cancelResourceUploads: any
}) {
  const theme = useTheme()
  const textRef = useRef(null)
  const [error, setError] = React.useState(null)
  const editMode = Boolean(public_id)
  const { isDarkTheme } = useAppTheme()

  const [text, setText] = React.useState(initialText)
  const characterLimit = 28
  const remainingCharacters = characterLimit - (text ? text.length : 0)

  React.useEffect(() => {
    if (open === false) {
      setError(null)
    }
  }, [open])

  React.useEffect(() => {
    setText(initialText)
  }, [initialText])

  const handleCancel = () => {
    if (resourceUploadProgress > 0 && resourceUploadProgress < 100) {
      // File is still uploading, cancel the upload
      initUploadResourceFiles() // This function needs to abort the upload and clean up
      cancelResourceUploads()
      console.log('Upload canceled.')
    }

    if (onCancel) {
      onCancel()
    }
    // Check if there is a current file and the upload is not in progress
    // if (currentFile && (resourceUploadProgress <= 0 || resourceUploadProgress >= 100)) {
    //   deleteFile(currentFile, false) // Assuming deleteFile method can handle file removal logic
    // }
  }

  const handleSave = () => {
    onSave({
      ...currentFile,
      public_id,
      url,
      title: textRef?.current?.value || '',
    })
    setText('')
    textRef.current.value = ''
  }
  const isResourceFileLoaded = resourceUploadProgress > 0 && resourceUploadProgress < 100
  return (
    open && (
      <Modal
        open={open}
        onClose={(e) => {
          textRef.current.value = ''
          setText('')
          handleCancel()
        }}
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            backgroundColor: theme.palette.background.paper,
            boxShadow: theme.shadows[5],
            padding: theme.spacing(2, 4, 3),
            borderRadius: '12px',
            width: '500px',
          }}
        >
          <DialogTitle
            sx={{
              borderBottom: 'none',
              padding: '12px 0px',
              fontFamily: 'Graphik SemiBold',
              fontSize: '18px',
              fontHeight: '24px',
            }}
          >
            {editMode ? 'Edit ' : 'Add '} File
          </DialogTitle>
          {Boolean(subtitle) && (
            <Typography
              sx={{
                fontFamily: 'Graphik Regular',
                fontSize: '14px',
                fontHeight: '16px',
                color: theme.palette.text.disabled,
              }}
            >
              {subtitle}
            </Typography>
          )}
          <Box sx={{ mt: '12px' }}>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'end' }}>
              <Typography
                paragraph
                sx={{
                  color: '#8D94A3',
                  fontSize: '12px',
                  fontFamily: 'Graphik Regular',
                  fontWeight: '500',
                  marginBottom: '5px',
                  marginRight: '3px',
                }}
              >
                {remainingCharacters}
              </Typography>
              <FormControl sx={{ width: '100%' }} className={'form-control'}>
                <TextField
                  type="text"
                  inputRef={textRef}
                  autoFocus
                  inputProps={{
                    maxLength: characterLimit,
                  }}
                  InputProps={{
                    style: {
                      top: '-2px',
                      backgroundColor: theme.palette.mode === 'dark' ? '#2e2f34' : '#ebefef',
                      borderRadius: '12px',
                      height: '48px',
                    },
                  }}
                  placeholder="Enter title"
                  id="text"
                  variant="outlined"
                  onChange={(e) => setText(e.target.value)}
                  defaultValue={text || currentFile?.title}
                  sx={{
                    backgroundColor: theme.palette.action.disabledBackground,
                    borderRadius: '12px',
                    height: '48px',
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: 'none',
                    },
                  }}
                />
              </FormControl>
            </Box>
            <Box sx={{ mt: '16px' }}>
              {currentFile ? (
                <>
                  <Box sx={{ display: 'flex', justifyContent: 'left' }}>
                    <FileAttachments
                      files={[currentFile]}
                      onDelete={(file) => deleteFile(file, false)}
                      showEdit={false}
                      showFilename={true}
                      isModal={true}
                      inputTitle={text}
                    />
                  </Box>
                </>
              ) : (
                <Box
                  className="background-color18 border-color02"
                  sx={{
                    mt: '16px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '12px',
                    borderStyle: 'dashed',
                    borderWidth: '1px',
                    height: '280px',
                    backgroundColor:
                      theme.palette.mode === 'dark'
                        ? 'rgba(46,47,52,0.7) !important'
                        : '#ebefef !important',
                  }}
                >
                  <AppDropzone
                    width={550}
                    height={280}
                    file={currentFile}
                    accept={['*'] as any}
                    onDropFile={(file) => handleFileSelect(file, textRef?.current?.value || '')}
                    noLimit={true}
                    placeholder={
                      <Box
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mt: isResourceFileLoaded ? undefined : '44px',
                        }}
                      >
                        {isResourceFileLoaded ? (
                          <Box sx={{ width: '100%' }}>
                            <Typography
                              sx={{
                                color: isDarkTheme ? 'rgba(255,255,255,0.87)' : 'rgba(0,0,0,0.87)',
                                fontSize: '14px',
                                fontWeight: 500,
                                mb: '6px',
                                textAlign: 'center',
                              }}
                              className="font-family-graphik-medium"
                            >
                              Uploading...
                              {resourceUploadProgress > 100
                                ? 100
                                : Math.round(resourceUploadProgress)}
                              %
                            </Typography>
                            <LinearProgress
                              color="inherit"
                              variant="determinate"
                              value={resourceUploadProgress}
                              sx={{
                                width: '326px',
                                mb: '6px',
                                '.MuiLinearProgress-bar': {
                                  backgroundColor: `${theme.palette.primary.main} !important`,
                                },
                              }}
                            />
                            <Typography
                              sx={{
                                fontFamily: 'Graphik Regular',
                                fontSize: '12px',
                                fontWeight: 400,
                                lineHeight: '16px',
                                color: theme.palette.text.disabled,
                                textAlign: 'center',
                              }}
                            >
                              {capitalizeAllWords(currentFile?.name || '')}
                            </Typography>
                          </Box>
                        ) : (
                          <>
                            <Box
                              sx={{
                                backgroundColor: theme.palette.action.disabledBackground,
                                width: '64px',
                                height: '64px',
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                color: theme.palette.text.disabled,
                              }}
                            >
                              <SVGUpload width={24} height={24} />
                            </Box>
                            <Typography
                              sx={{
                                fontFamily: 'Graphik Semibold',
                                fontSize: '16px',
                                lineHeight: '20px',
                                color: theme.palette.text.primary,
                                mt: '16px',
                              }}
                            >
                              Drag and drop a file to upload
                            </Typography>
                            {/*  <Typography
                                        sx={{
                                          fontFamily: 'Graphik Regular',
                                          fontSize: '14px',
                                          lineHeight: '16px',
                                          color: theme.palette.text.disabled,
                                          mt: '12px',
                                        }}
                                      >
                                        .mp3, .wav, .m4a, .aac
                                      </Typography> */}
                            <Button
                              sx={{
                                mt: '24px',
                                mb: '48px',
                                borderRadius: '10px',
                              }}
                              color="primary"
                            >
                              Select File
                            </Button>
                          </>
                        )}
                      </Box>
                    }
                  />
                </Box>
              )}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'end',
                  alignItems: 'center',
                  marginTop: '24px',
                  marginBottom: '5px',
                }}
              >
                {error && (
                  <Box sx={{ marginRight: '16px' }}>
                    <Typography
                      color="error"
                      sx={{
                        fontFamily: 'Graphik Regular',
                        fontSize: '12px',
                        fontHeight: '14px',
                      }}
                    >
                      {error}
                    </Typography>
                  </Box>
                )}
                <Button
                  onClick={handleCancel}
                  className="round-small"
                  variant="outlined"
                  color="inherit"
                  sx={{
                    color: theme.palette.mode === 'dark' ? 'rgb(141, 148, 163)' : '#000000',
                    height: '48px',
                    width: '128px',
                    borderRadius: '10px !important',
                    marginRight: '1px',
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  className="round-small"
                  variant="contained"
                  color="primary"
                  sx={{
                    color: '#fff',
                    height: '48px',
                    width: '128px',
                    borderRadius: '10px !important',
                    backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                  }}
                >
                  Save
                </Button>
              </Box>
            </Box>
          </Box>
        </Box>
      </Modal>
    )
  )
}
