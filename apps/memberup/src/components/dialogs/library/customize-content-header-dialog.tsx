import React, { useEffect, useRef, useState } from 'react'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import TextField from '@mui/material/TextField'
import { HexColorPicker, HexColorInput } from 'react-colorful'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { Controller, useForm } from 'react-hook-form'
import { useAppSelector } from '@/memberup/store/hooks'
import {
  getContentLibrary,
  selectContentLibrary,
} from '@/memberup/store/features/contentLibrarySlice'
import { updateContentLibraryApi } from '@memberup/shared/src/services/apis/content-library.api'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { IMAGE_ACCEPT_ONLY } from '@memberup/shared/src/types/consts'
import { InteractiveImageCropperCustomizeContent } from '@/memberup/components/ui/InteractiveImageCropper'
import { useAppDispatch } from '@/memberup/store/store'
import UnsplashSelector from '@/memberup/components/common/unsplash-selector'
import { makeStyles } from '@mui/styles'
import { Menu, Modal, Stack, ToggleButton, ToggleButtonGroup, useMediaQuery } from '@mui/material'
import useTheme from '@mui/material/styles/useTheme'
import useAppTheme from '../../hooks/use-app-theme'
import SVGCloseNew from '../../svgs/close-new'
import SVGUnsplash from '../../svgs/unsplash'
import SVGPhotoAddSmall from '../../svgs/photo-add-small'
import LibrarySkeleton from '../../library/library-loader'
import { darken } from '@mui/system'
import { FormatAlignCenter, FormatAlignLeft, FormatAlignRight } from '@mui/icons-material'
import NextImage from 'next/image'
import SVGPhoto from '@/shared-components/svgs/photo'
import LoadingButton from '@mui/lab/LoadingButton'
import clsx from 'clsx'
import CustomTooltip from '../../common/custom-tooltip'
import { selectMembership } from '@/memberup/store/features/membershipSlice'

/* export const CustomTooltip = styled(({ className, ...props }: TooltipProps) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.mode == 'dark' ? '#212124' : '#FFF',
    border: `1px solid #8D94A312`,
    color: theme.palette.text.primary,
    boxShadow: theme.shadows[4],
    fontSize: '12px',
    fontFamily: 'Graphik Regular',
    borderRadius: '12px',
    padding: '12px',
  },
})) */

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      maxWidth: 600,
      alignItems: 'center',
    },
    '& .MuiBackdrop-root': {
      backgroundColor: 'transparent',
      opacity: 1,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
    '& .MuiSelect-select': {
      paddingLeft: 8,
      paddingTop: 8,
      paddingBottom: 8,
    },
    '& .MuiInputBase-root': {
      color: 'inherit',
      width: '100%',
    },
  },
  circle: {
    width: '19px',
    height: '19px',
    borderRadius: '50%',
    border: '1px solid #FFFFFF60',
    outlineOffset: '2px',
    cursor: 'pointer',
    '&.firstColor:not(.darkTheme)': {
      borderColor: '#00000060',
    },
  },

  inputLabel: {
    fontSize: '14px',
    fontFamily: 'Graphik Medium',
    lineHeight: '16px',
    color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.87)' : '#000000',
  },
  labelContainer: {
    marginTop: '16px',
    marginBottom: '8px',
  },
  subLabel: {
    color: '#8D94A3',
    fontSize: '12px',
    fontFamily: 'Graphik Regular',
    fontWeight: '500',
    position: 'relative',
  },
  textField: {
    '& .MuiOutlinedInput-root': {
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
        borderRadius: '12px',
      },
      '&.Mui-focused': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderRadius: 3,
        },
      },
    },
    '& .MuiOutlinedInput-input': {
      color: theme.palette.text.primary,
      backgroundColor: theme.palette.mode == 'dark' ? '#212225' : '#f2f2f3',
      borderRadius: '12px',
      height: '34px',
      boxSizing: 'content-box',
    },
    '& .MuiOutlinedInput-inputMultiline': {
      height: 'auto',
      padding: '10px 12px',
    },
  },
  multilineTextField: {
    '& .MuiOutlinedInput-root': {
      padding: '0px',
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
        borderRadius: '12px',
      },
      '&.Mui-focused': {
        '& .MuiOutlinedInput-notchedOutline': {
          borderRadius: 3,
        },
      },
    },
    '& .MuiOutlinedInput-inputMultiline': {
      padding: '10px 12px',
      height: 'auto',
    },
    '& .MuiOutlinedInput-input': {
      color: theme.palette.text.primary,
      backgroundColor: theme.palette.mode == 'dark' ? '#212225' : '#f2f2f3',
      borderRadius: '12px',
      boxSizing: 'content-box',
      padding: '10px 12px',
    },
  },
  colorPicker: {
    '& .react-colorful': {
      borderRadius: '12px',
      width: '100% !important',
      height: '123px !important',
    },
    '& .react-colorful__pointer': {
      width: '26px !important',
      height: '26px !important',
    },
  },
  hexInput: {
    '& input': {
      border: 'solid 1px rgba(141, 148, 163, 0.08)',
      borderRadius: '12px',
      height: '46px',
      width: '100%',
      backgroundColor: theme.palette.mode == 'dark' ? '#202125' : '#dddddd',
      color: theme.palette.text.primary,
      padding: '0px 12px',
      '&:focus-visible': {
        outline: 'none',
      },
      '&:focus': {
        border: theme.palette.mode == 'dark' ? 'solid 1px #fff' : 'solid 1px #000000',
      },
    },
  },
}))

interface CustomizeContentHeaderDialogProps {
  open: boolean
  onClose: () => void
}

type FormDataType = {
  title?: string
  description?: string
  backgroundColor?: any
  meatadata?: any
  textColor?: string
  alignment?: string
  hasBeenModified?: boolean
}

const NoImageBackground = ({ backgroundColor, theme }) => {
  return (
    <Box
      sx={{
        border: `1px solid #8D94A312`,
        width: '100%',
        height: '100%',
        borderRadius: '16px',
        flexGrow: 1,
        backgroundColor: backgroundColor ? backgroundColor : theme.palette.primary.main,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      <Box sx={{ zIndex: 10 }}>
        <Stack
          spacing={2}
          sx={{
            position: 'absolute',
            top: -150,
            right: 130,
            rotate: '13deg',
            display: { sm: 'none', md: 'none', lg: 'block' },
          }}
        >
          <LibrarySkeleton isPreview width={180} height={270} />
          <LibrarySkeleton isPreview width={180} height={270} />
        </Stack>
      </Box>
      <Box sx={{ zIndex: 10 }}>
        <Stack
          spacing={2}
          sx={{
            position: 'absolute',
            top: -230,
            right: -80,
            rotate: '13deg',
            display: { sm: 'none', md: 'none', lg: 'block' },
          }}
        >
          <LibrarySkeleton isPreview width={180} height={270} />
          <LibrarySkeleton isPreview width={180} height={270} />
        </Stack>
      </Box>
      <Box
        sx={{
          borderRadius: '50%',
          height: '100%',
          width: '60%',
          position: 'absolute',
          right: '40px',
          top: '0',
          filter: 'blur(50px)',
          webkitFilter: 'blur(50px)',
          zIndex: 1,
          backgroundColor: backgroundColor
            ? darken(backgroundColor, 0.25)
            : darken(theme.palette.primary.main, 0.25),
        }}
      />
    </Box>
  )
}

// Helper function to calculate number of lines
const calculateLines = (element) => {
  // Save original styles
  const originalStyle = element.style.cssText

  // Temporarily change styles for calculation
  element.style.cssText = 'white-space: nowrap; overflow: visible; display: inline;'

  // Calculate lines
  const style = window.getComputedStyle(element, null)
  const lineHeight = parseInt(style.getPropertyValue('line-height'))
  const lines = Math.ceil(element.scrollHeight / lineHeight)

  // Revert styles back to original
  element.style.cssText = originalStyle

  return lines
}

export const ContentHeaderText = ({
  alignment,
  title,
  textColor,
  description,
  titleStyles = {},
  subtitleStyles = {},
  clamp = true,
  style = {},
  configurationType = 'Text',
  instance = 'customize',
}) => {
  const alignMapper = {
    left: 'start',
    center: 'center',
    right: 'end',
  }

  const titleRef = useRef(null)

  return (
    <Box
      className={`text-${alignment}`}
      sx={{
        position: 'absolute',
        width: '100%',
        height: '100%',
        p: instance === 'customize' ? '24px' : '0px',
        top: '0px',
        m: instance === 'customize' ? (alignment === 'left' ? '0px 10px' : '0px') : '0px',
        display: 'flex',
        justifyContent: alignMapper[alignment],
        zIndex: 11,
        pointerEvents: 'none',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          width: '42%',
          height: '100%',
          overflow: 'hidden',
          wordWrap: 'break-word',
          justifyContent: 'center',
          marginRight: instance === 'display' ? (alignment === 'right' ? '4%' : '0px') : undefined,
          marginLeft: instance === 'display' ? (alignment === 'left' ? '4%' : '0px') : undefined,
        }}
      >
        <Typography
          ref={titleRef}
          sx={{
            fontFamily: 'Graphik SemiBold',
            fontWeight: '500',
            fontSize: '13px',
            color: textColor,
            zIndex: 100,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 3,
            WebkitBoxOrient: 'vertical',
            ...titleStyles,
          }}
          gutterBottom
          style={(style as any)?.title}
        >
          {title}
        </Typography>
        <Typography
          sx={{
            fontFamily: 'Graphik Regular',
            fontWeight: '500',
            fontSize: '8px',
            lineHeight: '12px',
            color: textColor,
            zIndex: 1000,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 3,
            WebkitBoxOrient: 'vertical',
            ...subtitleStyles,
          }}
          variant="h6"
        >
          {description}
        </Typography>
      </Box>
    </Box>
  )
}
const CustomizeContentHeaderDialog: React.FC<CustomizeContentHeaderDialogProps> = ({
  open,
  onClose,
}) => {
  const { contentLibrary } = useAppSelector((state) => selectContentLibrary(state))
  const [anchorEl, setAnchorEl] = useState(null)
  const theme = useTheme()
  const [colorAttribute, setColorAttribute] = useState<'textColor' | 'backgroundColor'>(
    'backgroundColor'
  )
  const [activeTab, setActiveTab] = useState('Text')

  const { control, handleSubmit, setValue, watch } = useForm<FormDataType>({
    defaultValues: {
      hasBeenModified: contentLibrary.metadata?.hasBeenModified || false,
      title: contentLibrary.metadata?.hasBeenModified
        ? contentLibrary.metadata?.title
          ? contentLibrary.metadata?.title
          : ''
        : contentLibrary.metadata?.title
          ? contentLibrary.metadata?.title
          : 'Content Library',

      description: contentLibrary.metadata?.hasBeenModified
        ? contentLibrary.metadata?.description
          ? contentLibrary.metadata?.description
          : ''
        : contentLibrary.metadata?.description ??
          'A super cool hub where you can dive into awesome content and courses, rock your progress tracking, and discover handy resources!',
      backgroundColor: contentLibrary.metadata?.backgroundColor || null,
      textColor: contentLibrary.metadata?.textColor || '#ffffff',
      alignment: contentLibrary.metadata?.alignment || 'left',
    },
  })

  const membership = useAppSelector((state) => selectMembership(state))
  const { contentLibrary: library } = useAppSelector((state) => selectContentLibrary(state))
  const { uploadFiles, handleUploadFiles, initUploadFiles } = useUploadFiles('library')
  const [loading, setLoading] = useState(false)
  const [croppedImageBlobUrl, setCroppedImageBlobUrl] = useState<string | null>(
    contentLibrary.metadata?.croppedImg?.url
  )
  const [configurationType, setConfigurationType] = useState(
    contentLibrary.metadata?.configurationType
  )
  const [originalImageUrl, setOriginalImageUrl] = useState<string | null>(
    contentLibrary.metadata?.originalImg?.url
  )
  const [croppedArea, setCroppedArea] = useState(contentLibrary.metadata?.croppedImg?.croppedArea)
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(
    contentLibrary.metadata?.croppedImg?.croppedAreaPixels
  )
  const { isDarkTheme } = useAppTheme()
  const [isCustomColorSelected, setIsCustomColorSelected] = useState(
    contentLibrary.metadata.backgroundColor ? true : false
  )
  const dispatch = useAppDispatch()
  const [uploadZoom, setUploadZoom] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const descriptionValue = watch('description')
  const titleValue = watch('title')

  const classes = useStyles()

  const isSaveDisabled = originalImageUrl && !croppedImageBlobUrl

  useEffect(() => {
    if (!isCustomColorSelected) {
      setValue('backgroundColor', null)
    }
  }, [theme.palette.primary.main, setValue, isCustomColorSelected])

  const onImageCropped = (blob, croppedAreaPixels, croppedArea, zoom) => {
    setCroppedImageBlobUrl(blob)
    setCroppedAreaPixels(croppedAreaPixels)
    setCroppedArea(croppedArea)
    setUploadZoom(zoom)
  }

  const handleDropFile = async (f) => {
    setActiveTab('Text')
    setLoading(true)
    setConfigurationType('Upload')
    const uploadedImageUrl = await handleUploadFiles([f], 'Cloudinary')
    setOriginalImageUrl(uploadedImageUrl?.[0]?.url)
    setLoading(false)
  }

  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab)
  }
  const handleClickCustomColor = (event, attribute) => {
    setColorAttribute(attribute)
    setAnchorEl(event.currentTarget)
  }

  const handleColorChange = (newColor: string) => {
    setValue(colorAttribute, newColor)
    setIsCustomColorSelected(true)
  }

  const presetBackgroundColors = [
    theme.palette.primary.main,
    '#3986ff',
    '#58b560',
    '#ffbe0c',
    '#ff1f6e',
    '#e7f0fd',
  ]
  const presetTextColors = [
    '#ffffff',
    '#000000',
    theme.palette.primary.main,
    '#3986ff',
    '#58b560',
    '#ffbe0c',
  ]

  const getImageDimensions = (url) =>
    new Promise((resolve, reject) => {
      let img = new Image()
      img.onload = () => resolve({ width: img.width, height: img.height })
      img.onerror = reject
      img.src = url
    })

  const handleFormSubmit = async (data: FormDataType, e: Event) => {
    e.preventDefault()
    try {
      setIsSubmitting(true)
      const handleImageUpload = async (
        imageUrl: string,
        croppedArea: any,
        croppedAreaPixels: any,
        zoom: number
      ) => {
        const responseCropped = await fetch(imageUrl)
        const blob = await responseCropped.blob()
        const file = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' })
        const croppedDimensions = await getImageDimensions(imageUrl)
        const uploadedImage = await handleUploadFiles([file as any], 'Cloudinary')
        return {
          url: uploadedImage[0].url,
          dimensions: croppedDimensions,
          croppedArea,
          croppedAreaPixels,
          zoom,
        }
      }

      const currentMetadata = {
        ...contentLibrary.metadata,
        originalImg: null,
        croppedImg: null,
      }

      if (croppedImageBlobUrl && originalImageUrl) {
        currentMetadata.originalImg = {
          url: originalImageUrl,
          dimensions: await getImageDimensions(originalImageUrl),
        }
        currentMetadata.croppedImg = await handleImageUpload(
          croppedImageBlobUrl,
          croppedArea,
          croppedAreaPixels,
          uploadZoom
        )
      }

      currentMetadata.configurationType = Boolean(configurationType) ? configurationType : 'Text'
      currentMetadata.title = data.title
      currentMetadata.description = data.description
      currentMetadata.backgroundColor = data.backgroundColor
      currentMetadata.textColor = data.textColor
      currentMetadata.hasBeenModified = true
      currentMetadata.alignment = data.alignment || 'left'

      await updateContentLibraryApi(library.id, {
        metadata: currentMetadata,
      })

      dispatch(getContentLibrary(membership.slug))

      if (activeTab === 'Text') {
        onClose()
      } else {
        setActiveTab('Text')
      }
    } catch (error) {
      console.error('error', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const CustomBackdrop: React.FC<{ onClose: () => void }> = ({ onClose }) => {
    const theme = useTheme()
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

    return (
      <>
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.01)',
            zIndex: -1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={onClose}
        >
          <div
            style={{
              backgroundColor: '#fff',
              borderRadius: '12px',
              width: isMobile ? '100%' : '640px',
              maxHeight: '100%',
              overflowY: 'auto',
              position: 'relative',
            }}
            onClick={(e) => e.stopPropagation()}
          ></div>
        </div>
        <IconButton
          color="inherit"
          size="medium"
          className="close large"
          onClick={onClose}
          aria-label="close"
          sx={{
            background: theme.palette.mode === 'dark' ? '#17171a' : '#ffffff',
            color: '#8D94A3',
            p: '14px',
            m: '6px',
            width: '40px',
            height: '40px',
            position: 'fixed',
            top: '8px',
            zIndex: 1400,
            '&.hover': {
              backgroundColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
            },
          }}
        >
          <SVGCloseNew fontSize={16} />
        </IconButton>
      </>
    )
  }
  const headerDimensions = {
    width: '635px',
    height: '174px',
  }
  const disableBackgroundInput = Boolean(originalImageUrl)

  return (
    <Modal
      open={open}
      onClose={onClose}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '16px',
        padding: '20px 20px 20px 20px',
      }}
    >
      <Box
        className={classes.root}
        sx={{
          borderRadius: '12px',
          width: '684px',
          bgcolor: 'background.paper',
          height: '612px',
          position: 'relative',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <CustomBackdrop onClose={onClose} />
        <form onSubmit={handleSubmit(handleFormSubmit as any)}>
          <Box sx={{ m: '0px 24px' }}>
            <Box sx={{ minHeight: '247px' }}>
              {['Text', 'Upload'].includes(activeTab) && (
                <>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', m: '24px 0px' }}>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: 'Graphik SemiBold',
                        fontSize: '18px',
                        fontWeight: '500',
                        lineHeight: '24px',
                      }}
                    >
                      Customize Content Header
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      borderRadius: '16px',
                      position: 'relative',
                      overflow: 'hidden',
                      ...headerDimensions,
                    }}
                  >
                    {activeTab == 'Text' && (
                      <ContentHeaderText
                        alignment={watch('alignment')}
                        title={titleValue}
                        textColor={watch('textColor')}
                        description={watch('description')}
                        configurationType={configurationType}
                      />
                    )}
                    {activeTab == 'Text' && originalImageUrl && (
                      <InteractiveImageCropperCustomizeContent
                        image={originalImageUrl}
                        onImageCropped={onImageCropped}
                        customizeAlignment={watch('alignment')}
                        initialCroppedArea={
                          contentLibrary.metadata?.croppedImg?.croppedArea && {
                            croppedArea: contentLibrary.metadata?.croppedImg?.croppedArea,
                            zoom: contentLibrary.metadata?.croppedImg?.zoom,
                          }
                        }
                        onCancel={() => {
                          setCroppedImageBlobUrl(null)
                          setOriginalImageUrl(null)
                          initUploadFiles()
                          setConfigurationType('Text')
                        }}
                        height={headerDimensions.height}
                        width={headerDimensions.width}
                        aspectRatioOverride={635 / 174}
                      />
                    )}
                    {activeTab == 'Text' && !originalImageUrl && (
                      <>
                        <Box
                          sx={{
                            position: 'absolute',
                            width: '100%',
                            height: '100%',
                          }}
                        >
                          <NoImageBackground
                            theme={theme}
                            backgroundColor={
                              watch('backgroundColor')
                                ? watch('backgroundColor')
                                : theme.palette.primary.main
                            }
                          />
                        </Box>
                        <Button
                          onClick={() => handleTabChange('Upload')}
                          sx={{
                            zIndex: 12,
                            position: 'absolute',
                            bottom: '13px',
                            height: '32px',
                            right: watch('alignment') === 'right' ? 'auto' : '15px',
                            left: watch('alignment') === 'right' ? '15px' : 'auto',
                            width: '90px',
                            p: '0px',
                            backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                            border: '2px solid',
                            borderColor: theme.palette.text.disabled,
                            borderRadius: '10px',
                            minHeight: '32px',
                            '&:hover': {
                              backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                            },
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: '13px',
                              fontFamily: 'Graphik Medium',
                              color: theme.palette.text.primary,
                              lineHeight: '16px',
                            }}
                          >
                            <span
                              style={{
                                marginRight: '8px',
                                position: 'relative',
                                top: '2px',
                              }}
                            >
                              <SVGPhoto width={13} height={13} />
                            </span>
                            Replace
                          </Typography>
                        </Button>
                      </>
                    )}
                    {activeTab == 'Upload' && (
                      <Box
                        className="background-color18 border-color02"
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '16px',
                          borderStyle: 'dashed',
                          borderWidth: '1px',
                          position: 'absolute',
                          zIndex: 50,
                          ...headerDimensions,
                        }}
                      >
                        <AppDropzone
                          file={uploadFiles[0]}
                          accept={IMAGE_ACCEPT_ONLY}
                          onDropFile={handleDropFile}
                          placeholder={
                            <Box
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                cursor: 'default',
                                position: 'relative',
                                width: '100%',
                                height: '100%',
                              }}
                            >
                              <SVGPhotoAddSmall styles={{ color: theme.palette.primary.main }} />
                              <Typography
                                sx={{
                                  fontFamily: 'Graphik Medium',
                                  fontSize: '14px',
                                  lineHeight: '16px',
                                  marginTop: '16px',
                                }}
                              >
                                Upload Cover Photo
                              </Typography>
                              <Typography
                                sx={{
                                  fontFamily: 'Graphik Regular',
                                  fontSize: '12px',
                                  color: '#8D94A3',
                                  marginTop: '8px',
                                }}
                              >
                                Recommended minimum size
                              </Typography>
                              <Typography
                                sx={{
                                  fontFamily: 'Graphik Regular',
                                  fontSize: '12px',
                                  color: '#8D94A3',
                                  marginTop: '2px',
                                }}
                              >
                                2300px x 630px
                              </Typography>
                              <Button
                                sx={{
                                  borderRadius: '12px',
                                  mt: '4px',
                                  left: '4px',
                                  position: 'relative',
                                }}
                              >
                                Select Image
                              </Button>
                              <Button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setOriginalImageUrl(null)
                                  handleTabChange('Unsplash')
                                }}
                                sx={{
                                  zIndex: 2,
                                  position: 'absolute',
                                  bottom: '13px',
                                  height: '32px',
                                  right: '5px',
                                  width: '147px',
                                  p: '0px',
                                  backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                                  border: '2px solid',
                                  borderColor: theme.palette.text.disabled,
                                  borderRadius: '10px',
                                  minHeight: '32px',

                                  '&:hover': {
                                    backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                    borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                                  },
                                }}
                              >
                                <Typography
                                  sx={{
                                    fontSize: '13px',
                                    fontFamily: 'Graphik Medium',
                                    color: theme.palette.text.primary,
                                    lineHeight: '16px',
                                  }}
                                >
                                  <span
                                    style={{
                                      marginRight: '8px',
                                      position: 'relative',
                                      top: '2px',
                                    }}
                                  >
                                    <SVGUnsplash width={13} height={13} />
                                  </span>
                                  Search Unsplash
                                </Typography>
                              </Button>
                              <IconButton
                                color="inherit"
                                size="medium"
                                className="close large"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  setOriginalImageUrl(null)
                                  handleTabChange('Text')
                                }}
                                aria-label="close"
                                sx={{
                                  background: theme.palette.mode === 'dark' ? '#17171a' : '#ffffff',
                                  color: '#8D94A3',
                                  p: '14px',
                                  m: '6px',
                                  width: '40px',
                                  height: '40px',
                                  position: 'fixed',
                                  top: '8px',
                                  zIndex: 1400,
                                  '&.hover': {
                                    backgroundColor:
                                      theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
                                  },
                                }}
                              >
                                <SVGCloseNew fontSize={16} />
                              </IconButton>
                            </Box>
                          }
                        />
                      </Box>
                    )}
                  </Box>

                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginTop: '16px',
                      gap: '16px',
                    }}
                  >
                    <Box
                      sx={{
                        width: '376px',
                      }}
                    >
                      <Box
                        className={classes.labelContainer}
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          width: '100%',
                          alignItems: 'center',
                        }}
                      >
                        <Typography className={classes.inputLabel}>Title</Typography>
                        <p className={classes.subLabel}>
                          {50 - (titleValue?.length ? titleValue?.length : 0)}
                        </p>
                      </Box>
                      <Controller
                        name="title"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                          <TextField
                            {...field}
                            placeholder="Content"
                            variant="outlined"
                            name="title"
                            InputProps={{
                              inputProps: {
                                maxLength: 50,
                                style: {
                                  height: '48px',
                                  paddingTop: '0px',
                                  paddingBottom: '0px',
                                  fontFamily: 'Graphik Regular',
                                  fontSize: '14px',
                                },
                              },
                            }}
                            fullWidth
                            className={classes.textField}
                          />
                        )}
                      />
                      <Box
                        className={classes.labelContainer}
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          width: '100%',
                          alignItems: 'center',
                        }}
                      >
                        <Typography className={classes.inputLabel}>Description</Typography>
                        <p className={classes.subLabel}>
                          {160 - (descriptionValue?.length ? descriptionValue?.length : 0)}
                        </p>
                      </Box>
                      <Controller
                        name="description"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                          <TextField
                            {...field}
                            placeholder="Add description..."
                            size="medium"
                            variant="outlined"
                            multiline={true}
                            minRows={5}
                            fullWidth
                            InputProps={{
                              inputProps: {
                                maxLength: 160,
                                style: {
                                  height: '115px',
                                  fontFamily: 'Graphik Regular',
                                  fontSize: '14px',
                                },
                              },
                            }}
                            className={classes.multilineTextField}
                            name="description"
                          />
                        )}
                      />
                    </Box>
                    <Box
                      sx={{
                        width: '244px',
                      }}
                    >
                      <Typography className={clsx(classes.inputLabel, classes.labelContainer)}>
                        Background Color
                      </Typography>
                      <Box
                        sx={{
                          height: '48px',
                          border: `1px solid #8D94A312`,
                          backgroundColor: 'inherit',
                          borderRadius: '12px',
                        }}
                      >
                        <CustomTooltip
                          PopperProps={{
                            modifiers: [
                              {
                                name: 'offset',
                                options: {
                                  offset: [0, -30], // [horizontal offset, vertical offset]
                                },
                              },
                            ],
                          }}
                          title={
                            disableBackgroundInput && 'Reset to default to change background color'
                          }
                        >
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center',
                              height: '100%',
                              m: '0px 14px',
                              position: 'relative',
                            }}
                          >
                            <Box
                              sx={{
                                position: 'absolute',
                                width: '100%',
                                height: '100%',
                                borderRadius: '12px',
                                zIndex: 1,
                                backgroundColor: 'transparent',
                                display: disableBackgroundInput ? 'unset' : 'none',
                              }}
                            ></Box>
                            <Box sx={{ display: 'flex', gap: '8px' }}>
                              {presetBackgroundColors.map((color, index) => (
                                <Box
                                  className={`${classes.circle} ${
                                    index === 0 ? 'firstColor' : ''
                                  } ${isDarkTheme ? 'darkTheme' : ''}`}
                                  onClick={() => {
                                    setValue('backgroundColor', color)
                                    setIsCustomColorSelected(true)
                                  }}
                                  sx={{
                                    backgroundColor: disableBackgroundInput ? `${color}50` : color,
                                    outline:
                                      !disableBackgroundInput &&
                                      (watch('backgroundColor') == color ||
                                        (watch('backgroundColor') === null && index === 0)) &&
                                      `2px solid ${isDarkTheme ? '#fff' : '#000'}`,
                                  }}
                                ></Box>
                              ))}
                            </Box>
                            <Box
                              className={classes.circle}
                              onClick={(e) => handleClickCustomColor(e, 'backgroundColor')}
                              sx={{
                                outline:
                                  !disableBackgroundInput &&
                                  watch('backgroundColor') !== null &&
                                  !presetBackgroundColors.includes(watch('backgroundColor')) &&
                                  `2px solid ${isDarkTheme ? '#fff' : '#000'}`,
                                position: 'relative',
                                opacity: disableBackgroundInput ? 0.5 : 1,
                              }}
                            >
                              <NextImage
                                src="/assets/default/images/icons/color-picker.png"
                                fill={true}
                                alt="color-picker-back"
                              />
                            </Box>
                          </Box>
                        </CustomTooltip>
                      </Box>
                      <Typography className={clsx(classes.inputLabel, classes.labelContainer)}>
                        Text Color
                      </Typography>
                      <Box
                        sx={{
                          height: '48px',
                          border: `1px solid #8D94A312`,
                          backgroundColor: 'inherit',
                          borderRadius: '12px',
                        }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            height: '100%',
                            m: '0px 14px',
                          }}
                        >
                          <Box sx={{ display: 'flex', gap: '8px' }}>
                            {presetTextColors.map((color, index) => (
                              <Box
                                className={`${classes.circle} ${index === 0 ? 'firstColor' : ''} ${
                                  isDarkTheme ? 'darkTheme' : ''
                                }`}
                                onClick={() => setValue('textColor', color)}
                                sx={{
                                  backgroundColor: color,
                                  outline:
                                    watch('textColor') == color &&
                                    `2px solid ${isDarkTheme ? '#fff' : '#000'}`,
                                }}
                              ></Box>
                            ))}
                          </Box>
                          <Box
                            className={classes.circle}
                            onClick={(e) => handleClickCustomColor(e, 'textColor')}
                            sx={{
                              outline:
                                !disableBackgroundInput &&
                                !presetTextColors.includes(watch('textColor')) &&
                                `2px solid ${isDarkTheme ? '#fff' : '#000'}`,
                              position: 'relative',
                            }}
                          >
                            <NextImage
                              src="/assets/default/images/icons/color-picker.png"
                              fill={true}
                              alt="color-picker-back"
                            />{' '}
                          </Box>
                        </Box>
                      </Box>
                      <Typography className={clsx(classes.inputLabel, classes.labelContainer)}>
                        Text Format
                      </Typography>
                      <Box
                        sx={{
                          height: '48px',
                          border: `1px solid #8D94A312`,
                          backgroundColor: 'inherit',
                          borderRadius: '12px',
                          p: '5px',
                        }}
                      >
                        <ToggleButtonGroup
                          value={watch('alignment')}
                          exclusive
                          onChange={(e, nv) => setValue('alignment', nv)}
                          aria-label="text alignment"
                          fullWidth
                          sx={{
                            height: '36px',
                            '& .Mui-selected': {
                              border: 'solid !important',
                              borderColor: isDarkTheme ? '#ffffff50' : '#00000050',
                              backgroundColor: 'transparent !important',
                              borderWidth: '1px !important',
                            },
                            '& .MuiToggleButton-root': {
                              border: '1px solid transparent',
                              borderWidth: '1px',
                              borderRadius: '12px !important',
                            },
                          }}
                        >
                          <ToggleButton value="left" aria-label="left aligned">
                            <FormatAlignLeft />
                          </ToggleButton>
                          <ToggleButton value="center" aria-label="centered">
                            <FormatAlignCenter />
                          </ToggleButton>
                          <ToggleButton value="right" aria-label="right aligned">
                            <FormatAlignRight />
                          </ToggleButton>
                        </ToggleButtonGroup>
                      </Box>
                      <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={() => setAnchorEl(null)}
                        autoFocus
                        sx={{
                          '& .MuiMenu-paper': {
                            p: '0px',
                            m: '0px',
                            bgcolor: 'background.paper',
                            backgroundImage: 'none',
                            borderRadius: '10px',
                            width: '220px',
                          },
                          '& .MuiList-root': {
                            p: '15px',
                            m: '0px',
                          },
                        }}
                      >
                        <div
                          className={classes.colorPicker}
                          style={{
                            position: 'relative',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                        >
                          <HexColorPicker
                            color={watch(colorAttribute)?.toString()}
                            onChange={handleColorChange}
                          />
                          <Box
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              width: '100%',
                              height: '46px',
                              marginTop: '10px',
                            }}
                          >
                            <Box
                              sx={{
                                width: '46px',
                                height: '46px',
                                backgroundColor: watch(colorAttribute)?.toString(),
                                borderRadius: '12px',
                                border: `2px solid ${
                                  theme.palette.mode === 'dark' ? '#fff' : '#000'
                                }`,
                              }}
                            ></Box>
                            <Box
                              className={classes.hexInput}
                              style={{
                                width: '133px',
                                height: '46px',
                              }}
                            >
                              <HexColorInput
                                color={watch(colorAttribute)?.toString()}
                                onChange={handleColorChange}
                                prefixed
                                alpha
                              />
                            </Box>
                          </Box>
                        </div>
                      </Menu>
                    </Box>
                  </Box>
                </>
              )}
              {activeTab === 'Unsplash' && (
                <>
                  <Box sx={{ m: '24px 0px' }}>
                    <Typography
                      sx={{
                        fontFamily: 'Graphik SemiBold',
                        fontSize: '18px',
                        fontWeight: '500',
                        lineHeight: '24px',
                      }}
                    >
                      Search Unsplash
                    </Typography>
                  </Box>
                  <Grid item xs={12}>
                    <UnsplashSelector
                      scrollHeight={390}
                      onSelect={(url) => {
                        setOriginalImageUrl(url)
                        setActiveTab('Text')
                        setConfigurationType('Unsplash')
                      }}
                    />
                  </Grid>
                </>
              )}
            </Box>
          </Box>
          <Box
            sx={{
              backgroundColor: 'transparent',
              display: 'flex',
              justifyContent: 'space-between',
              flexDirection: 'row-reverse',
              position: 'absolute',
              p: '0px 24px',
              bottom: '24px',
              width: '100%',
            }}
          >
            <Box>
              <Button
                onClick={() => {
                  if (['Unsplash'].includes(activeTab)) {
                    setActiveTab('Text')
                  } else {
                    onClose()
                  }
                }}
                color="primary"
                sx={{
                  color: isDarkTheme ? '#8D94A3' : '#585D66',
                  borderRadius: '12px',
                }}
              >
                {['Unsplash'].includes(activeTab) ? 'Back' : 'Cancel'}
              </Button>
              <LoadingButton
                type="submit"
                color="primary"
                variant="contained"
                sx={{
                  borderRadius: '12px',
                  color: '#fff',
                  backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                  boxShadow: 'none',
                }}
                loading={isSubmitting}
                disabled={isSaveDisabled}
              >
                {activeTab == 'Unsplash' ? 'Select Image' : 'Save Changes'}
              </LoadingButton>
            </Box>
            {configurationType && (
              <>
                {activeTab != 'Unsplash' && (
                  <Button
                    onClick={() => {
                      setCroppedImageBlobUrl(null)
                      setOriginalImageUrl(null)
                      setIsCustomColorSelected(false)
                      setValue('title', 'Content Library')
                      setValue(
                        'description',
                        'A super cool hub where you can dive into awesome content and courses, rock your progress tracking, and discover handy resources!'
                      )
                      setValue('backgroundColor', null)
                      setValue('textColor', '#ffffff')
                      setValue('alignment', 'left')
                      setConfigurationType('Text')
                      setValue('hasBeenModified', false)
                    }}
                    color="primary"
                    sx={{
                      alignText: 'left',
                      borderRadius: '12px',
                      backgroundColor: isDarkTheme ? '#202128' : '#8D94A324',
                      color: isDarkTheme ? '#fff' : '#585D66',
                      width: '180px',
                    }}
                  >
                    Reset to Default
                  </Button>
                )}
              </>
            )}
          </Box>
        </form>
      </Box>
    </Modal>
  )
}

export default CustomizeContentHeaderDialog
