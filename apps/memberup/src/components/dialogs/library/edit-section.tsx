import { FEED_TYPE_ENUM, THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import SVGClose from '@/memberup/components/svgs/close'
import * as React from 'react'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import Modal from '@mui/material/Modal'
import Box from '@mui/material/Box'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import IconButton from '@mui/material/IconButton'
import Grid from '@mui/material/Grid'
import FormControl from '@mui/material/FormControl'
import useTheme from '@mui/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { DialogTitle, ListItem, MenuItem, Select } from '@mui/material'
import { useRef } from 'react'
import SVGCloseNew from '../../svgs/close-new'
import { isUrlValid } from '@/memberup/libs/utils'
import SVGFile from '../../svgs/file'
import SVGFileChecked from '../../svgs/file-checked'
import useAppTheme from '../../hooks/use-app-theme'
import SVGChevronDown from '../../svgs/chevron-down'
import { Circle } from '@mui/icons-material'

export default function EditSectionModal({
  open,
  text,
  url,
  onCancel,
  onSave,
  section,
  disableChangeVisibility,
}) {
  const textRef = useRef(null)
  const urlRef = useRef(null)
  const [modalOpen, setModalOpen] = React.useState(false)
  const [error, setError] = React.useState(null)
  const { theme, isDarkTheme } = useAppTheme()
  const [currentVisibility, setCurrentVisibility] = React.useState(
    section?.visibility || 'published'
  )
  const [inputValue, setInputValue] = React.useState(section?.name || '') // New state for managing input value

  React.useEffect(() => {
    if (open === false) {
      setError(null)
    }
  }, [open])

  const useStyles = makeStyles((theme) => ({
    root: {},
    modal: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalContent: {
      backgroundColor: theme.palette.background.paper,
      //border: '2px solid #000',
      boxShadow: theme.shadows[5],
      padding: theme.spacing(2, 4, 3),
      borderRadius: 12, // Add border radius to modal
      maxWidth: '478px',
    },
    input: {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12, // Add border radius to input fields
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
    dialogTitle: {
      borderBottom: 'none',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
      padding: '12px 2px',
    },
    subLabel: {
      color: '#8D94A3',
      fontSize: '12px',
      fontFamily: 'Graphik Regular',
      fontWeight: '500',
    },
  }))

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
  }

  const handleInputChange = (event) => {
    setInputValue(event.target.value) // Update state on input change
  }

  const handleSave = () => {
    if (onSave) {
      const newName = textRef.current.value
      const newVisibility = currentVisibility
      onSave(newName, newVisibility)
    }
  }

  const classes = useStyles()

  return (
    open && (
      <div>
        <Modal
          open={open}
          onClose={handleCancel}
          aria-labelledby="add-new-link-modal"
          aria-describedby="add-new-link-form"
          className={classes.modal}
        >
          <Box className={classes.modalContent} style={{ position: 'relative' }}>
            <DialogTitle className={classes.dialogTitle}>
              <Box style={{ display: 'flex', width: '100%', alignItems: 'center' }}>
                <Typography variant="h5">Edit Section</Typography>
              </Box>
              <IconButton
                sx={{
                  backgroundColor: theme.palette.mode === 'dark' ? '#323235' : '#dfe3e4',
                  color: '#8d94a3',
                  position: 'absolute',
                  right: '-50px',
                  top: '0px',
                  zIndex: 10,
                  padding: '13px',
                }}
                aria-label="close"
                onClick={(e) => {
                  e.preventDefault()
                  handleCancel()
                }}
              >
                <SVGCloseNew />
              </IconButton>
            </DialogTitle>

            <Box
              sx={{
                paddingLeft: 24,
                paddingRight: 24,
                paddingTop: 24,
                paddingBottom: 24,
                marginTop: '15px',
              }}
            >
              <Grid container spacing={5}>
                <Grid item xs={12}>
                  <Box
                    sx={{ display: 'flex', justifyContent: 'space-between', marginBottom: '-2px' }}
                  >
                    <Typography
                      variant="body2"
                      sx={{ marginLeft: '3px', fontWeight: 600, fontSize: '14px' }}
                    >
                      Title
                    </Typography>
                    <Typography sx={{ marginRight: '3px' }} className={classes.subLabel}>
                      {55 - inputValue.length}
                    </Typography>
                  </Box>
                  <FormControl
                    sx={{ marginTop: '8px', height: '48px' }}
                    className={'form-control'}
                    fullWidth
                  >
                    <TextField
                      inputRef={textRef}
                      autoFocus
                      placeholder="Content"
                      id="title"
                      variant="outlined"
                      className={classes.input}
                      defaultValue={section?.name || ''}
                      onChange={handleInputChange}
                      inputProps={{ maxLength: 55 }}
                    />
                  </FormControl>
                </Grid>
                <Grid
                  sx={{
                    display: 'flex',
                    width: '100%',
                    justifyContent: 'space-between',
                    marginTop: '40px',
                    paddingLeft: '30px',
                  }}
                >
                  <Grid item>
                    <Select
                      variant="outlined"
                      disabled={disableChangeVisibility}
                      open={modalOpen}
                      onClose={() => setModalOpen(false)}
                      onOpen={() => setModalOpen(true)}
                      error={Boolean(error)}
                      value={currentVisibility || 'published'}
                      IconComponent={() => (
                        <div
                          style={{ cursor: disableChangeVisibility ? 'cursor' : 'pointer' }}
                          onClick={(e) => {
                            if (disableChangeVisibility) return
                            setModalOpen(true)
                          }}
                        >
                          <SVGChevronDown styles={{ color: 'rgb(141, 148, 163)' }} />
                        </div>
                      )}
                      onChange={(e) => {
                        setCurrentVisibility(e.target.value)
                      }}
                      onBlur={() => {}}
                      sx={{
                        backgroundColor: isDarkTheme ? '#313236' : '#f0f1f3',
                        zIndex: 1000,
                        height: '40px !important',
                        display: 'flex',
                        lineHeight: '14px',
                        padding: '0px 14px 0px 0px',
                        width: '146px',
                        alignItems: 'center',
                        borderRadius: '12px',
                        '& .MuiOutlinedInput-notchedOutline': {
                          border: 'none',
                        },
                        '& .MuiOutlinedInput-input': {
                          paddingRight: '0px !important',
                        },
                        '& .MuiListItem-root': {
                          lineHeight: '14px !important',
                        },
                      }}
                      data-cy="profile-personality-type-select-field"
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            borderRadius: '12px',
                            boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                            border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                            backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                            backgroundImage: 'unset',
                            padding: '6px',
                            '& li.Mui-selected': {
                              backgroundColor: isDarkTheme
                                ? '#1b1b1f !important'
                                : '#f8f8f8  !important',
                              backgroundImage: 'unset',
                              borderRadius: '12px',
                            },
                            '& .MuiList-root': {
                              padding: '0px',
                            },
                          },
                        },
                      }}
                    >
                      {[
                        { value: 'draft', label: 'Draft' },
                        { value: 'published', label: 'Published' },
                      ].map((option) => (
                        <MenuItem
                          key={option.value}
                          value={option.value}
                          sx={{
                            '&:hover': {
                              backgroundColor: isDarkTheme ? '#1b1b1f' : '#e8e8e8',
                              borderRadius: '12px',
                            },
                          }}
                        >
                          <ListItem
                            sx={{
                              justifyContent: 'left',
                              paddingLeft: '5px',
                              paddingTop: '0px',
                              fontFamily: 'Graphik Medium',
                              fontSize: '14px',
                              color:
                                option.value === 'published'
                                  ? isDarkTheme
                                    ? '#AEE78B'
                                    : '#48B705'
                                  : '#000',
                              '& .MuiListItem-root': {
                                lineHeight: '14px !important',
                              },
                              '&:hover': {
                                backgroundColor: 'transparent',
                              },
                            }}
                          >
                            {option.value == 'draft' ? (
                              <Circle
                                sx={{
                                  fontSize: '10px',
                                  color: 'rgb(231, 173, 139)',
                                  marginRight: '6px',
                                }}
                              />
                            ) : (
                              <Circle sx={{ fontSize: '10px', marginRight: '6px' }} />
                            )}
                            {option.label}
                          </ListItem>
                        </MenuItem>
                      ))}
                    </Select>
                  </Grid>
                  <Grid item>
                    <Grid
                      sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        flexDirection: 'row',
                      }}
                    >
                      <Button
                        onClick={handleCancel}
                        className="round-small"
                        color="inherit"
                        sx={{ color: 'rgb(141, 148, 163)' }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleSave}
                        variant="contained"
                        className="round-small"
                        color="primary"
                        sx={{
                          width: '90px',
                          fontSize: '14px',
                          color: '#fff',
                          backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                        }}
                      >
                        Save
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
              <Box sx={{ marginTop: '8px' }}>
                {error && (
                  <Typography variant="body2" color="error">
                    {error}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </Modal>
      </div>
    )
  )
}
