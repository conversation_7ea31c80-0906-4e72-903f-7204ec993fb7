import CloseIcon from '@mui/icons-material/Close'
import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Slide from '@mui/material/Slide'
import useTheme from '@mui/material/styles/useTheme'
import { TransitionProps } from '@mui/material/transitions'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>
  },
  ref: React.Ref<unknown>,
) {
  return <Slide direction="up" ref={ref} {...props} />
})

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      backgroundColor: 'transparent',
      width: 'auto',
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 0,
  },
  dialogContent: {
    lineHeight: 1,
    position: 'relative',
    display: 'flex',
    padding: 0,
  },
  section: {
    padding: '16px',
    borderRadius: 3,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: theme.palette.divider,
  },
}))

const SparkEmptyInfoDialog: React.FC<{
  open: boolean
  onClose: () => void
}> = ({ open, onClose }) => {
  const classes = useStyles()
  const theme = useTheme()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))

  return (
    <Dialog
      fullWidth={true}
      className={clsx(classes.root, { mobile: isSmDown })}
      open={open}
      onClose={onClose}
      TransitionComponent={isSmDown ? Transition : undefined}
      keepMounted
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="spark-empty-info"
    >
      <DialogTitle className={classes.dialogTitle} id="spark-empty-info">
        <IconButton size="small" aria-label="close" className="close large" onClick={() => onClose()}>
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Box
          sx={{
            padding: 3,
            paddingTop: '36px',
            width: 360,
            margin: 'auto',
            borderRadius: 3,
            backgroundColor: theme.palette.background.paper,
          }}
        >
          <Grid container spacing={2}>
            <Grid className="text-center" item xs={12}>
              <AppImg
                src="/assets/default/images/spark-icon-white.png"
                alt="MemberUp Spark Icon"
                width={18}
                height={18}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography sx={{ fontSize: '1rem' }} variant="h6" align="center" gutterBottom>
                About Spark
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography className="text-ssm" color="text.disabled" align="center" variant="body2">
                Bringing your community together
              </Typography>
              <br />
            </Grid>
            <Grid item xs={12}>
              <AppImg
                src={
                  membershipSetting?.theme_mode === THEME_MODE_ENUM.light
                    ? '/assets/default/images/spark-graphic-light.png'
                    : '/assets/default/images/spark-graphic-dark.png'
                }
                alt="MemberUp Spark Explainer"
                width={328}
                height={174}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography className={clsx(classes.section, 'text-ssm')} align="center" variant="body2">
                <span className="color03 inline">
                  A new Spark question is automatically posted to the home feed sidebar every&nbsp;
                </span>
                24 hours.
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography className={clsx(classes.section, 'text-ssm')} align="center" variant="body2">
                Visibility:&nbsp;
                <span className="color03 inline">
                  Answers are hidden to members. Answering a quesiton allows them to view other answers.
                </span>
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography className={clsx(classes.section, 'text-ssm')} align="center" variant="body2">
                Your Answer:&nbsp;
                <span className="color03 inline">Creators can set answers to Spark questions ahead of time.</span>
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default SparkEmptyInfoDialog
