import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import {
  selectRequestUpdateProfile,
  selectUser,
  selectUserProfile,
  updateUserProfile,
} from '@/memberup/store/features/userSlice'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { DefaultLightThemeOptions, DefaultThemeOptions } from '@memberup/shared/src/settings/theme'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { EditOutlined } from '@mui/icons-material'
import { FormControl, InputBase } from '@mui/material'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import Grid from '@mui/material/Grid'
import LoadingButton from '@mui/lab/LoadingButton'
import Typography from '@mui/material/Typography'
import useTheme from '@mui/material/styles/useTheme'
import clsx from 'clsx'
import { TAppCropArea } from '@memberup/shared/src/types/types'
import React, { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { joiResolver } from '@hookform/resolvers/joi'
import Joi from 'joi'
import { useFilePicker } from 'use-file-picker'
import { CropArea } from '@/shared-types/types'
import { SVGUpload } from '@memberup/shared/src/components/svgs/upload'
import { ProfilePictureInput } from '@/components/profile/profile-picture-input'

const getColorOptions = (themeOptions) => {
  return {
    backgroundColor: themeOptions.palette.background.paper,
    textColorPrimary: themeOptions.palette.text.primary,
    textColorSecondary: themeOptions.palette.text.secondary,
    textColorDisabled: themeOptions.palette.text.disabled,
  }
}

type FormDataType = {
  bio: string
  image?: string
  image_crop_area?: {
    x: number
    y: number
    width: number
    height: number
  }
}

const GettingStartedProfile: React.FC = () => {
  const dispatch = useAppDispatch()
  const mountedRef = useMounted(true)
  const [file, setFile] = useState<File>(null)
  const theme = useTheme()
  const requestUpdateProfile = useAppSelector((state) => selectRequestUpdateProfile(state))
  const requestUpdateProfileRef = useRef(false)
  useEffect(() => {
    if (open && requestUpdateProfileRef.current && !requestUpdateProfile) {
      // TO DO:
      // Handle success, close dialog and update database
    }
    requestUpdateProfileRef.current = requestUpdateProfile
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, requestUpdateProfile])

  const [openImageCropper, setOpenImageCropper] = useState<{
    url: string
    file?: File
    crop_area?: TAppCropArea
  }>(null)
  const FormSchema = Joi.object({
    image: Joi.string().required(),
    image_crop_area: Joi.object({
      x: Joi.number(),
      y: Joi.number(),
      width: Joi.number(),
      height: Joi.number(),
    }).required(),
  }).options({ allowUnknown: true })
  const user = useAppSelector((state) => selectUser(state))
  const profile = useAppSelector((state) => selectUserProfile(state))

  const form = useForm<FormDataType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      bio: profile?.bio || '',
      image: profile?.image || null,
      image_crop_area: profile?.image_crop_area || null,
    },
    resolver: joiResolver(FormSchema),
  })

  const formData = form.watch()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const activeThemeMode = membershipSetting.theme_mode || THEME_MODE_ENUM.dark

  const { control, trigger, register, setValue, handleSubmit, watch } = form
  const [charCount, setCharCount] = useState(0)
  const handleContinue = (fData: any) => {
    const data = {
      ...fData,
      completed_image: true,
    }

    if (file) {
      data.image_file = file
    }
    dispatch(updateUserProfile({ data }))
  }

  const onChangeProfilePicture = (image: string, cropArea: CropArea, file: any) => {
    setValue('image', image, { shouldDirty: true, shouldValidate: true })
    setValue('image_crop_area', cropArea, { shouldDirty: true, shouldValidate: true })
    setFile(file)
  }

  const detailsData = {
    ...user,
    profile: { ...profile, ...formData },
  }

  return (
    <>
      <Box>
        <form autoComplete="off" onSubmit={handleSubmit(handleContinue)}>
          <input type="hidden" {...register('image')} required />
          <input type="hidden" {...register('image_crop_area')} required />
          <Grid container justifyContent="center">
            <Grid item xs={8} sm={6}>
              <Box
                sx={{
                  position: 'relative',
                  left: '50%',
                  top: '50%',
                  transform: 'translate(-50%, -50%)',
                  '& img.user-icon': {
                    width: 'auto',
                    height: 'auto',
                  },
                }}
              >
                <ProfilePictureInput
                  className="mb-6 xl:mb-0"
                  onSelectPicture={onChangeProfilePicture}
                  user={detailsData}
                  variant="centered"
                />
              </Box>
            </Grid>
          </Grid>
          <Grid container justifyContent="center" sx={{ mt: '20px' }}>
            <Grid item xs={10} sm={10}>
              <Typography
                style={{
                  fontFamily: 'Graphik Semibold',
                  fontSize: '16px',
                  lineHeight: '20px',
                }}
                align="center"
                variant="h5"
              >
                Let's create your profile
              </Typography>
              <Typography
                style={{
                  fontFamily: 'Graphik regular',
                  fontSize: '13px',
                  lineHeight: '20px',
                  color: theme.palette.mode == THEME_MODE_ENUM.dark ? '#8E8E93' : '#585D66',
                }}
                align="center"
                variant="body1"
                sx={{ mt: 2, mb: 3 }}
              >
                Upload your photo so everyone can recognize you. Don&apos;t forget to add your bio.
              </Typography>
            </Grid>
          </Grid>
          <Grid container justifyContent="center">
            <Grid item xs={12} style={{ textAlign: 'right' }}>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: 'Graphik Regular',
                  fontSize: '12px',
                  fontWeight: 500,
                  color: '#8D94A3',
                }}
              >
                {150 - charCount}
              </Typography>
            </Grid>
            <Box
              style={{
                backgroundColor: 'rgba(141,148,163, 0.08)',
                width: '100%',
                height: 'auto',
                minHeight: '100px',
                maxHeight: '200px',
                overflow: 'auto',
                borderRadius: 12,
              }}
              sx={{ p: 3, mt: '8px' }}
            >
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                    <InputBase
                      multiline
                      placeholder="Add your bio"
                      style={{
                        borderStyle: 'none',
                        width: '100%',
                        fontFamily: 'Graphik Regular',
                      }}
                      error={Boolean(error)}
                      value={value}
                      // Add this attribute
                      inputProps={{ maxLength: 150 }}
                      onChange={(e) => {
                        onChange(e.target.value)
                        // Update the character count here
                        setCharCount(e.target.value.length)
                        trigger('bio') // manually trigger validation after change
                      }}
                      onBlur={onBlur}
                      data-cy="profile-about-me-text-field"
                    />
                  </FormControl>
                )}
                control={control}
                name="bio"
              />
            </Box>
          </Grid>
          <Grid container justifyContent="center" sx={{ mt: 4 }}>
            <LoadingButton
              disabled={!form.formState.isValid}
              className="round-small text-white-500 dark-text-white-500"
              variant="contained"
              type="submit"
              loading={requestUpdateProfile}
              loadingPosition="start"
              fullWidth
              data-cy="gt-theme-submit-button"
              sx={{
                py: '18px',
                borderRadius: '10px !important',
                backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
              }}
              style={{
                fontFamily: 'Graphik Semibold',
                fontSize: '14px',
              }}
            >
              All Set! Enter Community
            </LoadingButton>
          </Grid>
        </form>
      </Box>
    </>
  )
}

const GettingStartedUserSteps: React.FC<{ open: boolean }> = ({ open }) => {
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const activeThemeMode = membershipSetting.theme_mode || THEME_MODE_ENUM.dark

  const themeOptions =
    activeThemeMode === THEME_MODE_ENUM.dark ? DefaultThemeOptions : DefaultLightThemeOptions

  const colorOptions = getColorOptions(themeOptions)
  return (
    <Dialog
      fullWidth={true}
      open={open}
      aria-labelledby="assign-member-role-dialog-title"
      sx={{
        'z-index': '40 !important',
        '& .MuiDialog-paper': {
          backgroundColor: 'rgba(0,0,0,0)',
          height: '100%',
          width: '100%',
          maxHeight: '100%',
          maxWidth: '100%',
          m: 0,
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(75,75,75,0.3)',
          opacity: 1,
        },
      }}
    >
      <DialogContent
        sx={{
          display: 'flex',
          alignItems: 'center',
          position: 'relative',
          maxWidth: 1440,
          m: 'auto',
          p: '32px',
        }}
      >
        <Box
          sx={{
            backgroundColor: colorOptions.backgroundColor,
            borderRadius: '24px',
            color: colorOptions.textColorPrimary,
            width: '100%',
            m: 'auto',
            maxWidth: 456,
            p: '32px',
            pt: '38px',
            pb: '24px',
          }}
        >
          <GettingStartedProfile />
        </Box>
      </DialogContent>
    </Dialog>
  )
}

GettingStartedUserSteps.displayName = 'GettingStartedUserSteps'

export default GettingStartedUserSteps
