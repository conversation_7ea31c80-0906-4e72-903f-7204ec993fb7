import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Slide, {SlideProps} from '@mui/material/Slide'
import Typography from '@mui/material/Typography'
import {makeStyles} from '@mui/styles'
import clsx from 'clsx'
import React, {useEffect, useMemo, useState} from 'react'
import {Configure, Index, InstantSearch, useInstantSearch} from 'react-instantsearch'
import {useSelector} from 'react-redux'

import {useMounted} from '@memberup/shared/src/components/hooks/use-mounted'
import {searchClient} from '@memberup/shared/src/config/algolia-client'
import {getGradientColor, gradientToArray} from '@memberup/shared/src/libs/color'
import {useStore} from '@/hooks/useStore'
import FeedHits from '@/memberup/components/algolia/feed-hits'
import LibraryHits from '@/memberup/components/algolia/library-hits'
import MemberHits from '@/memberup/components/algolia/member-hits'
import SearchBox from '@/memberup/components/algolia/search-box'
import SearchEmptyState from '@/memberup/components/algolia/search-empty-state'
import SVGClose from '@/memberup/components/svgs/close'
import {selectMembersMap} from '@/memberup/store/features/memberSlice'
import {useAppSelector} from '@/memberup/store/hooks'

const useStyles = makeStyles((theme) => {
  let secondaryBackgroundColor =
    theme.components.MuiCssBaseline.styleOverrides['body']['& .background-gradient01']['background']
  const temp = gradientToArray(secondaryBackgroundColor)

  if (temp.length > 2) {
    secondaryBackgroundColor = getGradientColor(225, temp[1].color, temp[2].color, temp[3]?.color, 0, 50, 100)
  }

  return {
    root: {
      width: '100%',
      maxWidth: 400,
      marginLeft: 'auto',
      '& .MuiDialog-container': {
        alignItems: 'unset',
      },
      '& .MuiDialog-paperFullWidth': {
        background: secondaryBackgroundColor,
        width: '100%',
        height: '100%',
        maxHeight: '100%',
        margin: 0,
      },
      '& .MuiDivider-root': {
        borderColor: 'black',
      },
      '& .MuiOutlinedInput-root': {
        // backgroundColor: theme.palette.action.disabledBackground,
        borderRadius: 12,
        '& .MuiOutlinedInput-notchedOutline': {
          border: 'none',
        },
      },
    },
    dialogTitle: {
      borderBottom: 'none',
      paddingTop: 24,
    },
    dialogContent: {
      color: theme.palette.background.default,
      '&.MuiDialogContent-root': {
        padding: 24,
        paddingTop: 0,
      },
    },
    photoWrapper: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      margin: 'auto',
      width: 104,
      height: 104,
      overflow: 'hidden',
      backgroundColor: '#EBE2DD',
      borderRadius: '42%',
    },
    profilePicture: {
      width: 200,
      height: 200,
      objectFit: 'cover',
    },
    personIcon: {
      fontSize: 96,
      opacity: 0.2,
    },
    searchTypesWrapper: {
      marginTop: 12,
      marginBottom: 12,
    },
    searchResultWrapper: {
      height: 'calc(100% - 102px)',
    },
    indexWrapper: {
      marginTop: 24,
    },
    searchTypeButton: {
      fontFamily: 'Graphik Medium',
      fontSize: 13,
      minHeight: 24,
      '&.MuiButton-outlined': {
        backgroundColor: '#F5F5F5',
        color: '#585D66',
      },
    },
  }
})

const NoResults = ({ emptyResults, isSearchBoxEmpty }) => {
  const { status } = useInstantSearch()

  return <>{!['loading', 'stalled'].includes(status) && emptyResults && !isSearchBoxEmpty && <SearchEmptyState />}</>
}

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const Search: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const membership = useStore((state) => state.community.membership)
  const [searchIndex, setSearchIndex] = useState<string>('all')
  const [isSearchBoxEmpty, setIsSearchBoxEmpty] = useState(true)
  const searchStatus = useSelector((state: any) => state.ui.searchStatus)
  const emptyResults = searchStatus?.feed === false && searchStatus?.member === false && searchStatus?.library === false
  const members = useAppSelector((state) => selectMembersMap(state))

  useEffect(() => {
    if (mountedRef.current) {
      setIsSearchBoxEmpty(true)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open])

  useEffect(() => {
    if (mountedRef.current) {
      if (isSearchBoxEmpty) {
        setSearchIndex('all')
      }
    }
  }, [isSearchBoxEmpty])

  const renderSearchType = useMemo(() => {
    return (
      <Grid container spacing={1}>
        <Grid item>
          <Button
            className={clsx(classes.searchTypeButton, 'round-small')}
            variant={!isSearchBoxEmpty && searchIndex === 'all' ? 'contained' : 'outlined'}
            color="primary"
            disabled={isSearchBoxEmpty}
            size="small"
            onClick={() => setSearchIndex('all')}
          >
            All
          </Button>
        </Grid>
        <Grid item>
          <Button
            className={clsx(classes.searchTypeButton, 'round-small')}
            variant={!isSearchBoxEmpty && searchIndex === 'content-library' ? 'contained' : 'outlined'}
            color="primary"
            disabled={isSearchBoxEmpty}
            size="small"
            onClick={() => setSearchIndex('content-library')}
          >
            Content
          </Button>
        </Grid>
        <Grid item>
          <Button
            className={clsx(classes.searchTypeButton, 'round-small')}
            variant={!isSearchBoxEmpty && searchIndex === 'member' ? 'contained' : 'outlined'}
            color="primary"
            disabled={isSearchBoxEmpty}
            size="small"
            onClick={() => setSearchIndex('member')}
          >
            Members
          </Button>
        </Grid>
        <Grid item>
          <Button
            className={clsx(classes.searchTypeButton, 'round-small')}
            variant={!isSearchBoxEmpty && searchIndex === 'feed' ? 'contained' : 'outlined'}
            color="primary"
            disabled={isSearchBoxEmpty}
            size="small"
            onClick={() => setSearchIndex('feed')}
          >
            Posts
          </Button>
        </Grid>
      </Grid>
    )
  }, [searchIndex, isSearchBoxEmpty, classes.searchTypeButton])

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 500,
          enter: 500,
          exit: 300,
        },
      }}
      aria-labelledby="edit-profile-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-profile-dialog-title">
        <IconButton size="small" aria-label="close" className="close" onClick={onClose}>
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        {searchClient && open && (
          <InstantSearch searchClient={searchClient} indexName={searchIndex === 'all' ? 'member' : searchIndex}>
            <Configure filters={searchIndex !== 'member' ? `viewable_by:${membership?.id}` : ''} />
            <SearchBox setIsSearchBoxEmpty={setIsSearchBoxEmpty} />
            <Divider />
            <Box className={classes.searchTypesWrapper}>{renderSearchType}</Box>
            <Box className={classes.searchResultWrapper}>
              {isSearchBoxEmpty ? (
                <Typography align="center" color="secondary" style={{ marginTop: '40px', color: '#000' }}>
                  Start typing to view results
                </Typography>
              ) : (
                <>
                  {(searchIndex === 'all' || searchIndex === 'member') && (
                    <div className={classes.indexWrapper}>
                      <Index indexName="member">
                        <MemberHits
                          displayEmptyResult={searchIndex !== 'all' && searchIndex === 'member'}
                          onClickSearchResultHandler={onClose}
                        />
                      </Index>
                    </div>
                  )}
                  {(searchIndex === 'all' || searchIndex === 'feed') && (
                    <div className={classes.indexWrapper}>
                      <Index indexName="feed">
                        <FeedHits
                          displayEmptyResult={searchIndex !== 'all' && searchIndex === 'feed'}
                          members={members}
                        />
                      </Index>
                    </div>
                  )}
                  {(searchIndex === 'all' || searchIndex === 'content-library') && (
                    <div className={classes.indexWrapper}>
                      <Index indexName="content-library">
                        <LibraryHits
                          displayEmptyResult={searchIndex !== 'all' && searchIndex === 'content-library'}
                          onClickSearchResultHandler={onClose}
                        />
                      </Index>
                    </div>
                  )}
                </>
              )}
              <NoResults emptyResults={emptyResults} isSearchBoxEmpty={isSearchBoxEmpty} />
            </Box>
          </InstantSearch>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default Search
