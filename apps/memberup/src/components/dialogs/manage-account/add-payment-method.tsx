import EditPaymentMethod from '@/memberup/components/dialogs/settings/edit-payment-method'
import SVGClose from '@/memberup/components/svgs/close'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import IconButton from '@mui/material/IconButton'
import { makeStyles } from '@mui/styles'
import React from 'react'
import 'react-phone-number-input/style.css'

const useStyles = makeStyles(() => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
    },
    '& .MuiDialogTitle-root .MuiTypography-h6': {
      fontSize: 16,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
}))

const AddPaymentMethod: React.FC<{
  open: boolean
  isMembership: boolean
  onClose: (paymentMethod?: any) => void
}> = ({ open, isMembership, onClose }) => {
  const classes = useStyles()

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      aria-labelledby="edit-payment-info-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="edit-payment-info-dialog-title">
        Payment Method
        <IconButton
          size="small"
          aria-label="close"
          className="close color03"
          onClick={(e) => onClose()}
        >
          <SVGClose fontSize={16} />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <EditPaymentMethod
          onCancel={() => onClose()}
          onSuccess={(pm) => onClose(pm)}
          isMembership={isMembership}
        />
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(AddPaymentMethod)
