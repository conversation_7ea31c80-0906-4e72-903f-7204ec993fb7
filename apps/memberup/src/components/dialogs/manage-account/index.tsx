import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import SVGClose from '@/memberup/components/svgs/close'
import { selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import IconButton from '@mui/material/IconButton'
import Slide, { SlideProps } from '@mui/material/Slide'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import { makeStyles } from '@mui/styles'
import React, { useMemo, useState } from 'react'
import Billing from './billing'
import EditAccount from './edit-account'
import EditNotifications from './edit-notifications'
import EditPrivacy from './edit-privacy'

const useStyles = makeStyles((theme) => ({
  root: {
    width: '50%',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    '& .MuiDialog-container': {
      alignItems: 'unset',
      backgroundColor: theme.palette.background.paper,
    },
    '& .MuiDialog-paper': {
      backgroundColor: 'transparent',
      boxShadow: 'none',
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: 650,
      margin: 0,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    paddingTop: 64,
    paddingLeft: 0,
    paddingBottom: 0,
    width: '100%',
    maxWidth: 552,
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  dialogContent: {
    minHeight: 320,
    lineHeight: 1,
    padding: 16,
    '&.MuiDialogContent-root': {
      paddingTop: 16,
    },
    '& img': {
      borderRadius: 16,
    },
    '& form': {
      display: 'flex',
      flexDirection: 'column',
      height: '100%',
    },
    '& .formContent': {
      height: '100%',
      overflow: 'auto',
    },
    '& .buttonsWrapper': {
      paddingTop: 24,
      paddingBottom: 8,
      borderTopColor: theme.palette.divider,
      borderTopStyle: 'solid',
      borderTopWidth: 1,
    },
  },
  title: {
    marginBottom: 14,
  },
  tabsWrapper: {
    maxWidth: 552,
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  content: {
    height: 'calc(100% - 43px)',
    maxWidth: 552,
    marginLeft: 'auto',
    marginRight: 'auto',
    paddingTop: 32,
  },
}))

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const ManageAccount: React.FC<{
  open: boolean
  onClose: () => void
}> = ({ open, onClose }) => {
  const classes = useStyles()
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const { isAdminOrCreator } = useCheckUserRole()
  const [activeTab, setActiveTab] = useState('notifications')

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 500,
          enter: 500,
          exit: 300,
        },
      }}
      aria-labelledby="manage-account-dialog-title"
      data-cy="manage-account-dialog"
    >
      <DialogTitle className={classes.dialogTitle} id="manage-account-dialog-title">
        <IconButton
          size="medium"
          aria-label="close"
          className="close large color03"
          style={{ top: 16 }}
          onClick={onClose}
        >
          <SVGClose fontSize={16} />
        </IconButton>
        Manage Account
      </DialogTitle>
      <DialogContent id="profile-dialog-content" className={classes.dialogContent}>
        <Box className={classes.tabsWrapper}>
          <Tabs
            value={activeTab}
            indicatorColor="primary"
            textColor="primary"
            onChange={(event, newValue) => setActiveTab(newValue)}
          >
            <Tab
              value="notifications"
              label="Notifications"
              data-cy="manage-account-notifications"
              sx={{ fontSize: '0.875rem' }}
            />
            <Tab
              sx={{ fontSize: '0.875rem' }}
              value="account"
              label="Account"
              data-cy="manage-account-account"
            />
            <Tab
              sx={{ fontSize: '0.875rem' }}
              value="privacy"
              label="Privacy"
              data-cy="manage-account-privacy"
            />
            {!isAdminOrCreator && Boolean(userProfile?.stripe_payment_method_id) && (
              <Tab value="billing" label="Billing" data-cy="manage-account-billing" />
            )}
          </Tabs>
          <Divider />
        </Box>

        <Box className={classes.content}>
          {activeTab === 'notifications' && <EditNotifications handleCancel={() => onClose()} />}
          {activeTab === 'account' && <EditAccount />}
          {activeTab === 'privacy' && <EditPrivacy handleCancel={() => onClose()} />}
          {activeTab === 'billing' && <Billing />}
        </Box>
      </DialogContent>
    </Dialog>
  )
}

export default ManageAccount
