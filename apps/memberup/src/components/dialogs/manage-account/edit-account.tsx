// import CancelFeedback from './cancel-feedback'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { getActiveUser } from '@/memberup/store/features/userSlice'
import { useAppDispatch } from '@/memberup/store/hooks'
import ChevronRightIcon from '@mui/icons-material/ChevronRight'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import { useState } from 'react'
import CancelMembership from './cancel-membership'
import ChangeEmailPasswordForm from './change-email-password-form'

const useStyles = makeStyles((theme) => ({
  root: {
    color: theme.palette.text.disabled,
    overflow: 'hidden',
  },
  section: {
    borderColor: theme.palette.divider,
    borderRadius: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    padding: 16,
  },
}))

export default function EditAccount() {
  const classes = useStyles()
  const dispatch = useAppDispatch()
  const { isAdminOrCreator } = useCheckUserRole()
  const [openCancelMembership, setOpenCancelMembership] = useState(false)
  // const [openCancelFeedback, setOpenCancelFeedback] = useState(false)

  return (
    <Box className={classes.root}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Box className={classes.section}>
            <ChangeEmailPasswordForm onSuccess={() => {}} />
          </Box>
        </Grid>
        {!isAdminOrCreator && (
          <Grid item xs={12}>
            <Box className={classes.section}>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1">Cancel Your Account</Typography>
                </Grid>
                {/* <Grid item xs={12}>
                  <Typography variant="body2" color="inherit">
                    You will have acess until the end of your billing period.
                  </Typography>
                </Grid> */}
                <Grid item xs={12}>
                  <Divider />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    className="no-padding"
                    variant="text"
                    color="error"
                    endIcon={<ChevronRightIcon />}
                    onClick={() => setOpenCancelMembership(true)}
                  >
                    Cancel Membership
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Grid>
        )}
      </Grid>
      {openCancelMembership && (
        <CancelMembership
          open={openCancelMembership}
          onClose={(cancelled) => {
            setOpenCancelMembership(false)
            if (cancelled) {
              dispatch(getActiveUser({}))
              // setOpenCancelFeedback(true)
            }
          }}
        />
      )}
      {/* {openCancelFeedback && (
        <CancelFeedback open={openCancelFeedback} onClose={() => setOpenCancelFeedback(false)} />
      )} */}
    </Box>
  )
}
