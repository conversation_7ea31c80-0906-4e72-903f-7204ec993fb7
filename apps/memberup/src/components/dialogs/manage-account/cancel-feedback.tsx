import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { showToast } from '@memberup/shared/src/libs/toast'
import { addFeedbackApi } from '@memberup/shared/src/services/apis/user.api'
import { Feedbacks } from '@memberup/shared/src/settings/cancel-feedback'
import CloseIcon from '@mui/icons-material/Close'
import Button from '@mui/material/Button'
import Checkbox from '@mui/material/Checkbox'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import FormControlLabel from '@mui/material/FormControlLabel'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 12,
      textAlign: 'center',
    },
    '& .MuiFormControlLabel-root': {
      marginLeft: 42,
    },
    '& .MuiFormControlLabel-label': {
      lineHeight: 1,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    fontSize: 22,
    padding: 24,
  },
  dialogContent: {
    padding: 32,
  },
}))

const CancelFeedback: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const [requestFeedback, setRequestFeedback] = useState(false)
  const [selectedFeedbacks, setSelectedFeedbacks] = useState([0])

  const handleSubmit = () => {
    setRequestFeedback(true)
    addFeedbackApi(selectedFeedbacks.map((i) => Feedbacks[i]))
      .then((res) => {
        if (res.data.sucess) {
          showToast('Feedback sent', 'success', { autoClose: 3000, closeOnClick: true })
          onClose()
        }
      })
      .catch((err) => {
        showToast(err.message, 'error', { autoClose: 3000, closeOnClick: true })
      })
      .finally(() => {
        if (mountedRef.current) {
          setRequestFeedback(false)
        }
      })
  }

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      aria-labelledby="feedback-dialog"
    >
      <DialogTitle className={classes.dialogTitle} id="feedback-dialog">
        Sorry to see you go
        <IconButton
          size="small"
          aria-label="close"
          className="close large color03"
          onClick={(e) => onClose()}
        >
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <Grid container spacing={3}>
          <Grid item className="color02" xs={12}>
            <Typography variant="body1" color="inherit">
              Your subscription has beed cancelled.
            </Typography>
            <br />
          </Grid>
          <Grid item className="color02" xs={12}>
            <Typography variant="body1" color="inherit" gutterBottom>
              If you can take a moment to tell us how we can improve,
            </Typography>
            <Typography variant="body1" color="inherit" gutterBottom>
              we would really appreciate it.
            </Typography>
          </Grid>
          <Grid item xs={12}></Grid>
          <Grid item className="color02 text-left" xs={12}>
            <Grid container>
              {Feedbacks.map((f, index) => (
                <Grid key={`feedback-${index}`} item className="color02 text-left" xs={12}>
                  <FormControlLabel
                    key={`feedback-${index}`}
                    color="inherit"
                    control={
                      <Checkbox
                        size="small"
                        checked={selectedFeedbacks.indexOf(index) >= 0}
                        value={index}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedFeedbacks([...selectedFeedbacks, index])
                          } else {
                            setSelectedFeedbacks(selectedFeedbacks.filter((i) => i !== index))
                          }
                        }}
                      />
                    }
                    label={f}
                  />
                </Grid>
              ))}
            </Grid>
          </Grid>

          <Grid item xs={12}></Grid>
          <Grid item xs={12}>
            <Button
              className="app-button round-small"
              variant="outlined"
              color="inherit"
              fullWidth
              disabled={requestFeedback || !selectedFeedbacks?.length}
              onClick={handleSubmit}
            >
              {requestFeedback ? <CircularProgress size={16} color="inherit" /> : 'Send Feedback'}
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default CancelFeedback
