import useChange from '@/memberup/components/hooks/use-change'
import {
  selectMembershipSettingUpdateStatus,
  selectRequestUpdateMembershipSetting,
  updateMembershipSetting,
} from '@/memberup/store/features/membershipSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import useTheme from '@mui/material/styles/useTheme'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { joiResolver } from '@hookform/resolvers/joi'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { IStripePrice } from '@memberup/shared/src/types/interfaces'
import CloseIcon from '@mui/icons-material/Close'
import InfoIcon from '@mui/icons-material/Info'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import InputAdornment from '@mui/material/InputAdornment'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { CSSObject } from '@mui/material/styles'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import React, { useEffect } from 'react'
import { Controller, useForm } from 'react-hook-form'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      backgroundImage: 'none',
      borderRadius: 12,
    },
    '& .MuiInputBase-root': {
      width: '100%',
      paddingLeft: 8,
    },
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
    fontSize: 16,
  },
  dialogContent: {
    minHeight: 280,
    lineHeight: 1,
    padding: '0 24px 24px 24px',
    '& .MuiOutlinedInput-root': {
      backgroundColor: (theme.components.MuiCssBaseline.styleOverrides as CSSObject).body[
        '& .background-color06'
      ]['backgroundColor'],
      borderRadius: 12,
    },
    '& .MuiOutlinedInput-notchedOutline': {
      border: 'none',
    },
  },
  dialogAction: {
    backgroundColor: 'transparent',
  },
  section: {
    borderRadius: 12,
    padding: 16,
  },
}))

type PriceFormDataType = {
  price: number
}

const PriceFormValue: PriceFormDataType = {
  price: null,
}

const FormSchema = Joi.object({
  price: Joi.number().required().greater(0).messages({
    'number.greater': `Price must be greater than 0.`,
    'any.required': `Price is required.`,
  }),
}).options({ allowUnknown: true })

const MembershipPriceForm: React.FC<{
  price: IStripePrice
  loading: boolean
  onCancel: () => void
}> = ({ price, loading, onCancel }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const dispatch = useAppDispatch()

  const { control, reset, formState, handleSubmit } = useForm<PriceFormDataType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: PriceFormValue,
    resolver: joiResolver(FormSchema),
  })

  useEffect(() => {
    if (!mountedRef.current) return
    reset({
      price: price?.unit_amount ? price.unit_amount / 100 : null,
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [price])

  const handleFormSubmit = (formData) => {
    const unitAmount = Math.floor(parseFloat(formData.price) * 100)

    if (unitAmount !== price.unit_amount) {
      dispatch(
        updateMembershipSetting({
          data: {
            stripe_prices: [
              {
                id: price.id,
                active: false,
              },
              {
                active: true,
                unit_amount: unitAmount,
                currency: 'usd',
                recurring:
                  price.metadata['planType'] !== 'one_time'
                    ? {
                        interval: price.recurring.interval,
                      }
                    : undefined,
                type: price.metadata['planType'] === 'one_time' ? 'one_time' : 'recurring',
                metadata: price.metadata,
              },
            ],
          },
        })
      )
    }
  }

  return (
    <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
      <Box className={clsx(classes.section, 'background-color08')} style={{ marginBottom: 8 }}>
        <Typography className="bold" variant="body1" gutterBottom fontWeight="bold">
          Price per {price.recurring?.interval || 'lifetime'}
        </Typography>
        <Controller
          render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
            <TextField
              placeholder="Enter Price"
              error={Boolean(error)}
              helperText={error?.message}
              size="small"
              variant="outlined"
              value={value}
              fullWidth
              InputProps={{
                startAdornment: <InputAdornment position="start">$</InputAdornment>,
              }}
              style={{ maxWidth: 236 }}
              onChange={onChange}
              onBlur={onBlur}
            />
          )}
          control={control}
          name="price"
        />
      </Box>
      <br />
      <br />
      <Divider />
      <br />
      <br />
      <Grid container spacing={2}>
        <Grid item className="color03">
          <InfoIcon fontSize="small" />
        </Grid>
        <Grid item xs>
          <Typography variant="body1">
            Repricing only applies to new members, you currently cannot change the price for
            existing members.
          </Typography>
        </Grid>
      </Grid>
      <br />
      <br />
      <Divider />
      <br />
      <br />
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <Button
            className="round-small"
            variant="outlined"
            disabled={loading}
            fullWidth
            onClick={onCancel}
          >
            Cancel
          </Button>
        </Grid>
        <Grid item xs={6}>
          <Button
            className="round-small"
            sx={{
              backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
              color: '#fff',
            }}
            fullWidth
            type="submit"
            variant="contained"
            disabled={loading}
          >
            {loading ? <CircularProgress size={16} /> : 'Save Changes'}
          </Button>
        </Grid>
      </Grid>
    </form>
  )
}

const SetMembershipPriceDialog: React.FC<{
  open: boolean
  price: IStripePrice
  onClose: () => void
}> = ({ open, price, onClose }) => {
  const classes = useStyles()
  const requestUpdateMembershipSetting = useAppSelector((state) =>
    selectRequestUpdateMembershipSetting(state)
  )
  const membershipSettingUpdateStatus = useAppSelector((state) =>
    selectMembershipSettingUpdateStatus(state)
  )
  useChange(
    membershipSettingUpdateStatus,
    [requestUpdateMembershipSetting],
    membershipSettingUpdateStatus === 'updated',
    () => {
      onClose()
    }
  )

  return (
    <Dialog
      maxWidth="xs"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={onClose}
      aria-labelledby="set-membership-price-dialog-title"
    >
      <DialogTitle className={classes.dialogTitle} id="set-membership-price-dialog-title">
        Set your community price
        <IconButton
          size="small"
          aria-label="close"
          className="close large color02"
          onClick={onClose}
        >
          <CloseIcon fontSize="inherit" />
        </IconButton>
      </DialogTitle>
      <DialogContent className={classes.dialogContent}>
        <MembershipPriceForm
          price={price}
          loading={requestUpdateMembershipSetting}
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  )
}

export default SetMembershipPriceDialog
