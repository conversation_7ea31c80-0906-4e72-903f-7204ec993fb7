import useDialogClasses from '@/memberup/components/hooks/use-dialog-classes'
import useDialogFormClasses from '@/memberup/components/hooks/use-dialog-form-classes'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { joiResolver } from '@hookform/resolvers/joi'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { SVGUpload } from '@memberup/shared/src/components/svgs/upload'
import { getFirstLastName, validateEmail } from '@memberup/shared/src/libs/string-utils'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { showToast } from '@memberup/shared/src/libs/toast'
import { inviteApi } from '@memberup/shared/src/services/apis/invite.api'
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew'
import CloseIcon from '@mui/icons-material/Close'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import axios from 'axios'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import FormHelperText from '@mui/material/FormHelperText'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import Slide, { SlideProps } from '@mui/material/Slide'
import Step from '@mui/material/Step'
import StepLabel from '@mui/material/StepLabel'
import Stepper from '@mui/material/Stepper'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import useTheme from '@mui/material/styles/useTheme'
import Joi from 'joi'
import React, { useEffect, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import Link from 'next/link'

const steps = ['Add Emails', 'Message']

type EmailsFormDataType = {
  emails: string
}

const EmailsFormValue: EmailsFormDataType = {
  emails: '',
}

const EmailsFormSchema = Joi.object({
  emails: Joi.string().required().messages({
    'string.empty': `Please enter emails.`,
    'any.required': `Please enter emails.`,
  }),
}).options({ allowUnknown: true })

const EmailsForm = ({ onFormSubmit }) => {
  const { control, reset, formState, getValues, setValue, trigger, handleSubmit } =
    useForm<EmailsFormDataType>({
      mode: 'onBlur',
      reValidateMode: 'onBlur',
      defaultValues: EmailsFormValue,
      resolver: joiResolver(EmailsFormSchema),
    })
  const theme = useTheme()

  return (
    <Grid container>
      <Grid item xs={12}>
        <Typography
          className="bold"
          variant="h6"
          align="center"
          style={{ fontSize: 24, lineHeight: '32px', marginBottom: 16 }}
        >
          Copy and paste emails from a spreadsheets
        </Typography>
        <Typography
          color="text.disabled"
          variant="body1"
          align="center"
          sx={{ lineHeight: '20px' }}
        >
          Enter the emails of members you&rsquo;d like to invite, separated by a comma.
        </Typography>
      </Grid>
      <Grid item xs={12} sx={{ marginTop: '40px' }}>
        <form autoComplete="off" onSubmit={handleSubmit(onFormSubmit)}>
          <Grid container>
            <Grid item xs={12}>
              <Controller
                render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                  <FormControl error={Boolean(error)} className="'form-control light" fullWidth>
                    <TextField
                      placeholder="Enter email address"
                      variant="outlined"
                      error={Boolean(error)}
                      helperText={error?.message}
                      multiline={true}
                      minRows={8}
                      value={value}
                      onChange={(e) => {
                        onChange(e.target.value)
                      }}
                      onBlur={onBlur}
                    />
                  </FormControl>
                )}
                control={control}
                name="emails"
              />
              <Typography
                color="text.disabled"
                variant="body1"
                align="center"
                sx={{ marginTop: '16px', lineHeight: '20px' }}
              >
                Setup custom pricing for a customer group or run pricing experiments.
              </Typography>
            </Grid>
            <Grid item xs={12} sx={{ marginTop: '40px' }}>
              <Button
                sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                className="round-small"
                variant="contained"
                type="submit"
                fullWidth
              >
                Continue to Message
              </Button>
            </Grid>
          </Grid>
        </form>
      </Grid>
    </Grid>
  )
}

const UploadEmailsCSVForm = ({ onFormSubmit }) => {
  const mountedRef = useMounted(false)
  const [file, setFile] = useState<File>(null)
  const [error, setError] = useState<string>('')
  const theme = useTheme()

  useEffect(() => {
    if (!file) return
    const reader = new FileReader()
    reader.onload = (e) => {
      if (!mountedRef.current) return
      const temp = e.target.result as string
      const headers = temp.slice(0, temp.indexOf('\n')).split(',')
      const emailHeaderIndex = headers.findIndex((h) => h.toLowerCase().trim() === 'email')
      const nameHeaderIndex = headers.findIndex((h) => h.toLowerCase().trim() === 'name')

      if (emailHeaderIndex < 0 || nameHeaderIndex < 0) {
        setError(
          `You must have the first row contain the headers 'name' and 'email'. Please try again.`
        )
        setFile(null)
        return
      }

      const rows = temp
        .slice(temp.indexOf('\n') + 1)
        .split('\n')
        .filter((row) => row.trim() !== '')
      if (rows.length > 1000) {
        setError(`You can send a max number of 1000 invites at a time. Please try again.`)
        setFile(null)
        return
      }
      if (rows.length === 0) {
        setError(`There is no record in the input file. Please try again.`)
        setFile(null)
        return
      }

      const result = []
      const invalidRows = []
      if (rows.length > 0) {
        for (let i = 0; i < rows.length; i++) {
          const temp = rows[i].split(',')
          const email = temp[emailHeaderIndex]?.trim() || ''
          const name = temp[nameHeaderIndex].trim() || ''
          if (name && email && validateEmail(email)) {
            if (!result.find((r) => r.email === email)) {
              result.push({
                email,
                name,
              })
            }
          } else {
            invalidRows.push(i + 1)
          }
        }
      }

      if (invalidRows.length) {
        setError(
          `${invalidRows.length} Records have invalid or missing data at row(s) ${invalidRows.join(
            ', '
          )}. Please check the csv file and try again.`
        )
        setFile(null)
        return
      }
      onFormSubmit(result)
    }
    reader.onerror = (e) => {
      console.error('File reading error', reader.error)
      reader.abort()
    }
    reader.readAsText(file)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file])

  const downloadFile = async () => {
    try {
      const response = await axios.get('/assets/default/bulk-invite.csv', {
        responseType: 'blob',
      })
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', 'bulk-invite.csv')
      document.body.appendChild(link)
      link.click()
      link.parentNode.removeChild(link)
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  return (
    <Grid container>
      <Grid item xs={12}>
        <Typography
          className="bold"
          variant="h6"
          align="center"
          style={{ fontSize: 24, lineHeight: '32px', marginBottom: 16 }}
        >
          Upload a CSV file
        </Typography>
        <Typography
          color="text.disabled"
          variant="body1"
          align="center"
          sx={{ lineHeight: '20px' }}
        >
          Select a .csv file with two columns labeled "name" and "email." You may use either a first
          name or a full name. Refer to this{' '}
          <span
            style={{ color: theme.palette.primary.main, cursor: 'pointer' }}
            onClick={downloadFile}
          >
            sample file
          </span>{' '}
          as a guide to input your information.
        </Typography>
      </Grid>
      <Grid item xs={12} sx={{ marginTop: '40px' }}>
        <Box className="background-color15" sx={{ borderRadius: '12px', padding: '40px' }}>
          <AppDropzone
            file={file}
            accept={{ 'text/csv': ['.csv'], 'application/vnd.ms-excel': ['.csv'] }}
            onDropFile={(f) => {
              setError('')
              setFile(f)
            }}
            placeholderHeight={100}
            placeholder={
              <Box
                className="text-center"
                sx={{
                  '& svg': {
                    display: 'inline-block',
                  },
                }}
              >
                <SVGUpload width={24} height={24} />
                <Typography
                  className="bold"
                  variant="h6"
                  align="center"
                  style={{ lineHeight: '24px', marginTop: '24px' }}
                >
                  Drop CSV here to upload
                </Typography>
                <Button className="no-padding" variant="text" style={{ marginTop: '16px' }}>
                  Browse files
                </Button>
              </Box>
            }
          />
        </Box>
      </Grid>
      <Grid item xs={12} sx={{ marginTop: '16px' }}>
        <Typography
          color="text.disabled"
          variant="body1"
          align="center"
          sx={{ lineHeight: '20px' }}
        >
          Please Note: You can send up to 1000 invites at a time.
        </Typography>
      </Grid>
      {Boolean(error) && (
        <Grid item xs={12} sx={{ marginTop: '16px' }}>
          <FormHelperText error>{error}</FormHelperText>
        </Grid>
      )}
    </Grid>
  )
}

type MessageFormDataType = {
  message: string
}

const MessageFormValue: MessageFormDataType = {
  message: '',
}

const MessageForm = ({ inviteMembers, onClose }) => {
  const mountedRef = useMounted(true)
  const membership = useAppSelector((state) => selectMembership(state))
  const classes = useDialogFormClasses()
  const theme = useTheme()
  const [requestInvite, setRequestInvite] = useState(false)
  const { handleSubmit } = useForm<MessageFormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    defaultValues: MessageFormValue,
  })

  const handleFormSubmit = (data) => {
    setRequestInvite(true)
    inviteApi({ members: inviteMembers, message: data.message })
      .then((res) => {
        if (mountedRef.current && res.data.success) {
          showToast('Invitations sent', 'success')
          onClose()
        }
      })
      .catch((err) => {
        if (mountedRef.current) {
          setRequestInvite(false)
        }
        const errorMessage = err?.response?.data?.message || err?.message
        if (errorMessage) {
          showToast(errorMessage, 'error')
        }
      })
  }

  const isDisabledSave = requestInvite
  const firstName = getFirstLastName(inviteMembers?.[0]?.name)?.[0] || ''

  return (
    <Grid container>
      <Grid item xs={12}>
        <Typography
          className="bold"
          variant="h6"
          align="center"
          style={{ fontSize: 24, lineHeight: '32px', marginBottom: 16 }}
        >
          Your Message
        </Typography>
        <Typography
          color="text.disabled"
          variant="body1"
          align="center"
          sx={{ lineHeight: '20px' }}
        >
          Preview the message that will be sent in your invitation.
        </Typography>
      </Grid>
      <Grid item xs={12} sx={{ marginTop: '40px' }}>
        <form className={classes.form} autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
          <Box className="background-color19" sx={{ borderRadius: '12px', padding: 3 }}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                {/* <Grid container spacing={2} alignItems="center">
                  <Grid item>
                    <Button
                      className="color03"
                      variant="contained"
                      sx={{
                        pointerEvents: 'none',
                        minWidth: 40,
                        width: 40,
                        height: 40,
                        borderRadius: '20px',
                      }}
                    >
                      <EmailOutlinedIcon />
                    </Button>
                  </Grid>
                  <Grid item xs>
                    <Typography variant="subtitle1">
                      <b>Email</b>
                    </Typography>
                  </Grid>
                </Grid> */}
                <Typography className="bold" variant="subtitle1" sx={{ mb: 2 }}>
                  {inviteMembers?.length || 0}&nbsp; unique invite(s) will be sent.
                </Typography>
                <Divider
                  sx={{
                    marginLeft: '-16px',
                    marginRight: '-16px',
                    borderColor: theme.palette.background.default,
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <Grid container spacing={5}>
                  <Grid item xs={12}>
                    <Typography className="bold" variant="subtitle1" gutterBottom>
                      Message Preview (Can&apos;t be edited)
                    </Typography>
                    <Box
                      className="background-color18"
                      sx={{
                        borderRadius: '12px',
                        p: '16.5px 14px',
                      }}
                    >
                      <Typography variant="body1" sx={{ mb: '18px' }}>
                        Hey {firstName}&nbsp;👋,
                      </Typography>
                      <Typography variant="body1" sx={{ mb: '18px' }}>
                        You’ve been invited to join the {membership.name || ''} community on
                        MemberUp!
                      </Typography>
                      <Typography variant="body1" sx={{ textDecoration: 'underline' }}>
                        Click Here To Accept Your Invitation
                      </Typography>
                    </Box>
                    {/* <Controller
                      render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                        <FormControl
                          error={Boolean(error)}
                          className={clsx('form-control', 'light')}
                          fullWidth
                        >
                          <TextField
                            variant="outlined"
                            fullWidth
                            InputProps={{
                              readOnly: true,
                            }}
                            multiline={true}
                            minRows={6}
                            value={`Hey 👋,\n\nYou’ve been invited to join the ${
                              membership.name || ''
                            } community on Memberup!\n\nClick Here To Accept Your Invitation`}
                          />
                        </FormControl>
                      )}
                      control={control}
                      name="message"
                    /> */}
                  </Grid>
                </Grid>
              </Grid>
              <Grid item xs={12} sx={{ marginTop: '40px' }}>
                <Button
                  className="round-small"
                  variant="contained"
                  type="submit"
                  disabled={isDisabledSave}
                  fullWidth
                >
                  {requestInvite ? <CircularProgress size={16} /> : 'Send Invite'}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </form>
      </Grid>
    </Grid>
  )
}

// eslint-disable-next-line react/display-name
const Transition = React.forwardRef<unknown, SlideProps>((props, ref) => (
  <Slide direction="left" {...props} ref={ref} />
))

const BulkInviteMembersDialog: React.FC<{
  open: boolean
  uploadCSV: boolean
  onClose: () => void
}> = ({ open, uploadCSV, onClose }) => {
  const classes = useDialogClasses()
  const [activeStep, setActiveStep] = useState(0)
  const [inviteMembers, setInviteMembers] = useState([])

  const handleChange = (e: { email: string; name: string }[]) => {
    setInviteMembers(e)
    setActiveStep(1)
  }

  return (
    <Dialog
      maxWidth="lg"
      fullWidth={true}
      className={classes.root}
      open={open}
      onClose={() => onClose()}
      TransitionComponent={Transition}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 500,
          enter: 500,
          exit: 300,
        },
      }}
      aria-labelledby="create-discount-dialog-title"
    >
      <DialogContent id="create-discount-dialog-content" className={classes.dialogContent}>
        <Grid container>
          <Grid item xs={12}>
            <Box sx={{ padding: 2 }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item>
                  <IconButton
                    className="background-color15 color03"
                    size="large"
                    onClick={() => onClose()}
                  >
                    <ArrowBackIosNewIcon />
                  </IconButton>
                </Grid>
                <Grid item xs>
                  <Typography className="bold" variant="subtitle1">
                    <b>{activeStep === 0 ? 'Bulk Invite' : 'Bulk Invite'}</b>
                  </Typography>
                  <Typography color="text.disabled" variant="body1">
                    {activeStep === 0 ? 'Add Emails' : 'Message'}
                  </Typography>
                </Grid>
                <Grid item>
                  <IconButton size="large" aria-label="close" onClick={() => onClose()}>
                    <CloseIcon fontSize="inherit" />
                  </IconButton>
                </Grid>
              </Grid>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Stepper
              activeStep={activeStep}
              alternativeLabel
              sx={{ maxWidth: 280, margin: 'auto', marginTop: '32px' }}
            >
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ maxWidth: 552, margin: 'auto', marginTop: '40px' }}>
              {activeStep === 1 ? (
                <MessageForm inviteMembers={inviteMembers} onClose={onClose} />
              ) : (
                <>
                  {uploadCSV ? (
                    <UploadEmailsCSVForm onFormSubmit={handleChange} />
                  ) : (
                    <EmailsForm onFormSubmit={handleChange} />
                  )}
                </>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default BulkInviteMembersDialog
