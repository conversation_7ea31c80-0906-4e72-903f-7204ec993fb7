import React, { useEffect, useState } from 'react'
import <PERSON>actDOM from 'react-dom'

function WaitingPortal({ children, target }) {
  const [element, setElement] = useState(null)

  useEffect(() => {
    const intervalId = setTimeout(() => {
      const targetElement = document.getElementById(target)
      if (targetElement) {
        setElement(targetElement)
        clearTimeout(intervalId)
      }
    }, 100)

    return () => {
      clearTimeout(intervalId)
    }
  }, [target])

  if (!element) {
    return null
  }

  return (
    <div style={{ position: 'relative', zIndex: 9999 }}>
      {ReactDOM.createPortal(children, element)}
    </div>
  )
}

export default WaitingPortal
