//@ts-nocheck
import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import AddIcon from '@mui/icons-material/Add'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import Typography from '@mui/material/Typography'
import React, { useEffect, useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { ISparkCategory, ISparkQuestion, ISparkQuestionWithSettings } from '@memberup/shared/src/types/interfaces'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import SparkAnswerDialog from '@/memberup/components/dialogs/spark/spark-answer'
import SparkQuestionDialog from '@/memberup/components/dialogs/spark/spark-question'
import SparkQuestionListItem from '@/memberup/components/spark/spark-question-list-item'
import { moveArrayItem } from '@/memberup/libs/utils'
import { selectMembership, selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { getExpires } from '@/shared-libs/date-utils'
import {
  getCurrentSparkMembershipQuestionInstanceApi,
  getSparkQuestionsApi,
  updateSparkQuestionSequenceApi,
} from '@/shared-services/apis/spark.api'
import { DEFAULT_SPARK_EXPIRE_TIME, DEFAULT_SPRK_TIMEZONE } from '@/shared-settings/spark'

const getItemStyle = ({ transform, ...draggableStyle }: any, isDragging: boolean) => ({
  height: 'auto',
  userSelect: 'none',
  paddingLeft: 0,
  paddingRight: 0,
  paddingTop: 0,
  paddingBottom: 0,
  // background: isDragging ? 'rgb(240, 240, 240)' : 'inherit',
  transform: !transform
    ? `unset`
    : `translate(0, ${transform.substring(transform.indexOf(',') + 1, transform.indexOf(')'))})`,
  ...draggableStyle,
})

const getListStyle = (isDraggingOver) => ({
  paddingTop: 0,
  paddingBottm: 0,
  //background: isDraggingOver ? 'lightblue' : 'lightgrey',
})

const SparkQuestions: React.FC<{
  category: ISparkCategory
  questions: any
  setQuestions: any
}> = ({ category, questions, setQuestions }) => {
  const mountedRef = useMounted(true)
  const [activeQuestion, setActiveQuestion] = useState<ISparkQuestion>(null)

  const [activeQuestionType, setActiveQuestionType] = useState('Upcoming')
  const [openNewQuestion, setOpenNewQuestion] = useState(false)
  const [openAnswer, setOpenAnswer] = useState(null)
  const [requestGetQuestions, setRequestGetQuestions] = useState(true)
  const [activeSparkResponses, setActiveSparkResponses] = useState([])
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const membership = useAppSelector((state) => selectMembership(state))
  const [expiresIn, setExpiresIn] = useState('')

  const mCategory = category?.spark_m_categories?.[0]
  const activeQuestionId = (mCategory?.started && mCategory?.active_question_id) || null

  const fetchQuestions = async () => {
    if (!category?.id) return
    setRequestGetQuestions(true)
    const res = await getSparkQuestionsApi(category.id, activeQuestionType, membership.id)
    const docs = res.data.data.docs
    // Remove active question so we show at the top
    if (activeQuestionId) {
      const activeIndex = docs.findIndex((d) => d.id === activeQuestionId)
      if (activeIndex !== -1) {
        setActiveQuestion(docs[activeIndex])
        docs.splice(activeIndex, 1)
      }
    }
    setQuestions((prevValue) => ({
      docs: docs,
      total: res.data?.data?.total || 0,
    }))
    setRequestGetQuestions(false)
  }

  useEffect(() => {
    setActiveQuestion(null)
    setQuestions({
      docs: [],
      total: 0,
    })

    if (!category?.id) return

    fetchQuestions()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [category?.id, activeQuestionType])

  useEffect(() => {
    async function getActiveSparkResponses() {
      const result = await getCurrentSparkMembershipQuestionInstanceApi(membershipSetting.active_spark_category_id)
      const questionRes = result.data?.data?.question
      setActiveSparkResponses(questionRes?.spark_responses)
    }
    getActiveSparkResponses()
  }, [])

  useEffect(() => {
    const intervalId = setInterval(() => {
      const temp = getExpires(
        membershipSetting?.spark_expire_time || DEFAULT_SPARK_EXPIRE_TIME,
        membershipSetting?.time_zone || DEFAULT_SPRK_TIMEZONE,
        true,
      )
      setExpiresIn(temp)
      if (temp === 'Expired') {
        clearInterval(intervalId)
      }
    }, 1000)

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [membershipSetting?.spark_expire_time, membershipSetting?.time_zone])

  const handleQuestionUpdated = (updatedQuestion: ISparkQuestion, index?: number) => {
    if (!mountedRef.current) return
    if (index === null || index === undefined || index === -1) {
      setActiveQuestion(updatedQuestion)
      return
    }

    setQuestions((prevValue) => {
      const output = prevValue.docs.slice()
      output[index] = updatedQuestion
      return {
        docs: output,
        total: prevValue.total,
      }
    })
  }

  const onDragEnd = async (result) => {
    // Dropped outside the list
    if (!result.destination) {
      return
    }
    const temp = questions.docs || []
    const sourceIndex = result.source.index
    const sourceItem = temp[sourceIndex]
    const destinationIndex = result.destination.index

    if (sourceItem && destinationIndex) {
      try {
        await updateSparkQuestionSequenceApi(category.id, sourceItem.id, sourceIndex, destinationIndex, membership.id)
      } catch (err) {
        console.error(err)
      }
    }

    moveArrayItem(temp, sourceIndex, destinationIndex)

    setQuestions({
      total: questions.total,
      docs: temp,
    })
  }

  const editable = activeQuestionType === 'Upcoming'
  const draggable = editable && questions.docs.length > 1

  return (
    <Box sx={{ overflow: 'hidden' }} className="spark-question">
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <Typography variant="h4">Questions</Typography>
            </Grid>
            <Grid item xs>
              <IconButton
                className="background-gradient01"
                aria-controls="add-menu"
                aria-haspopup="true"
                onClick={() => setOpenNewQuestion({})}
              >
                <AddIcon />
              </IconButton>
            </Grid>
            <Grid item>
              <Box
                sx={{
                  borderRadius: '12px',
                  borderColor: 'rgba(141,148,163,0.12)',
                  borderStyle: 'solid',
                  borderWidth: 1,
                  padding: '4px',
                  width: { xs: 202 },
                }}
              >
                <Grid container>
                  <Grid item xs={6}>
                    <Button
                      className={
                        activeQuestionType === 'Upcoming' ? 'round-small background-color15' : 'round-small color03'
                      }
                      variant="text"
                      color="inherit"
                      fullWidth
                      sx={{ height: 24, fontSize: 12 }}
                      onClick={() => {
                        if (!requestGetQuestions) {
                          setActiveQuestionType('Upcoming')
                        }
                      }}
                    >
                      Upcoming
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      className={
                        activeQuestionType === 'Completed' ? 'round-small background-color15' : 'round-small color03'
                      }
                      variant="text"
                      color="inherit"
                      fullWidth
                      sx={{ height: 24, fontSize: 12 }}
                      onClick={() => {
                        if (!requestGetQuestions) {
                          setActiveQuestionType('Completed')
                        }
                      }}
                    >
                      Completed
                    </Button>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          {Boolean(!requestGetQuestions && !questions.docs.length) && (
            <Typography
              className="color03"
              variant="body1"
              style={{ marginTop: 48, marginBottom: '48px', textAlign: 'center' }}
            >
              No questions completed yet. {!mCategory?.started && 'Start a question pack to Spark engagement ⚡'}
            </Typography>
          )}

          {activeQuestion && activeQuestionType !== 'Completed' && (
            <Box
              className="background-gradient01"
              sx={{
                borderRadius: '12px',
                backgroundColor: 'rgba(0, 0, 0, 0.4) !important',
                backgroundBlendMode: 'darken',
                position: 'relative',
                minHeight: 100,
              }}
            >
              <SparkQuestionListItem
                question={activeQuestion}
                draggable={false}
                editable={editable}
                isPrimary={true}
                onOpenAnswer={() => setOpenAnswer(activeQuestion)}
                callbackUpdate={(updatedQuestion) => handleQuestionUpdated(updatedQuestion)}
                activeSparkResponses={activeSparkResponses}
                expiresIn={expiresIn}
              />
            </Box>
          )}

          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="questionDroppable">
              {(provided, snapshot) => (
                <List
                  dense
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  style={getListStyle(snapshot.isDraggingOver)}
                >
                  {questions.docs.map((q, i) => {
                    return (
                      <Draggable
                        key={q.id}
                        index={i}
                        draggableId={q.id}
                        isDragDisabled={questions.docs.length === 1 || !draggable}
                      >
                        {(providedDraggable, snapshotDraggable) => (
                          <ListItem
                            className="background-color06"
                            {...providedDraggable.draggableProps}
                            {...providedDraggable.dragHandleProps}
                            ref={providedDraggable.innerRef}
                            style={getItemStyle(providedDraggable.draggableProps.style, snapshotDraggable.isDragging)}
                            sx={{
                              borderRadius: '12px',
                              position: 'relative',

                              '&:after': {
                                content: '""',
                                backgroundColor:
                                  q.spark_m_questions?.[0]?.active === undefined ||
                                  Boolean(q.spark_m_questions?.[0]?.active)
                                    ? 'RGB(242, 152, 201, 0.5)'
                                    : 'RGB(242, 152, 201, 0.2)',
                                width: '5px',
                                height: 'calc(100% - 10px)',
                                position: 'absolute',
                                top: 5,
                                borderTopLeftRadius: 12,
                                borderBottomLeftRadius: 12,
                              },
                            }}
                          >
                            <SparkQuestionListItem
                              key={questions.id}
                              question={q}
                              draggable={draggable}
                              editable={editable}
                              isDragging={snapshotDraggable.isDragging}
                              onOpenAnswer={() => setOpenAnswer(q)}
                              callbackUpdate={(updatedQuestion) => handleQuestionUpdated(updatedQuestion, i)}
                            />
                          </ListItem>
                        )}
                      </Draggable>
                    )
                  })}
                  {provided.placeholder}
                </List>
              )}
            </Droppable>
          </DragDropContext>
        </Grid>
      </Grid>
      {openAnswer && (
        <SparkAnswerDialog
          question={openAnswer}
          open={true}
          onClose={(updatedQuestion) => {
            if (!mountedRef.current) return
            if (!updatedQuestion) {
              setOpenAnswer(null)
              return
            }

            if (updatedQuestion.id === activeQuestionId) {
              setActiveQuestion(updatedQuestion)
            } else {
              setQuestions((prevValue) => {
                const index = questions.docs.findIndex((q) => q.id === updatedQuestion.id)
                const output = prevValue.docs.slice()
                output[index] = updatedQuestion
                return {
                  docs: output,
                  total: prevValue.total,
                }
              })
            }
            setOpenAnswer(null)
          }}
        />
      )}
      {openNewQuestion && (
        <SparkQuestionDialog
          categoryId={category?.id}
          question={openNewQuestion}
          open={true}
          onClose={(newQuestion) => {
            if (!mountedRef.current) return
            if (newQuestion) {
              if ((newQuestion as ISparkQuestionWithSettings).id) {
                setQuestions((prevValue) => {
                  const output = prevValue.docs.slice()
                  output.push(newQuestion)
                  return {
                    docs: output,
                    total: output.length,
                  }
                })
              } else {
                setQuestions((prevValue) => ({
                  docs: (prevValue.docs || []).concat(newQuestion as ISparkQuestion),
                  total: prevValue.total + 1,
                }))
              }
            }
            setOpenNewQuestion(null)
          }}
          onReset={(e) => {
            if (!mountedRef.current) return
            if (e?.question_id) {
              const tempIndex = questions.docs.findIndex((q) => q.id === e.id)
              setQuestions((prevValue) => ({
                docs: prevValue.docs.slice(0, tempIndex).concat(
                  {
                    ...openNewQuestion,
                  },
                  prevValue.docs.slice(tempIndex + 1),
                ),
                total: prevValue.total + 1,
              }))
            }
            setOpenNewQuestion(null)
          }}
        />
      )}
      {requestGetQuestions && <LoadingSpinner />}
    </Box>
  )
}

export default SparkQuestions
