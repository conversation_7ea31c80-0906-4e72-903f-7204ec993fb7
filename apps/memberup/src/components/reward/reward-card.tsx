import { selectMembershipAssetsPath } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import LinearProgress from '@mui/material/LinearProgress'
import Typography from '@mui/material/Typography'
import useTheme from '@mui/material/styles/useTheme'
import clsx from 'clsx'
import React from 'react'

const RewardCard: React.FC<{ reward: any }> = ({ reward }) => {
  const theme = useTheme()
  const membershipAssetsPath = useAppSelector((state) => selectMembershipAssetsPath(state))

  return (
    <Card
      className="app-image-wrapper"
      sx={{
        borderWidth: 0,
        borderRadius: '8px',
        width: '100%',
        lineHeight: 1,
      }}
    >
      <CardContent
        sx={{
          color: theme.palette.text.secondary,
          p: '12px',
          '&:last-child': {
            pb: '12px',
          },
        }}
      >
        <Grid container spacing={2}>
          <Grid item>
            <AppImg
              height={80}
              width={80}
              src={`${membershipAssetsPath}/images/upcoming_event_1.png`}
              alt="Upcoming Event"
            />
          </Grid>
          <Grid className={clsx('d-flex', 'direction-column', 'justify-center')} item xs>
            <Typography variant="body2">
              <b>Botanical Cash Envelope Set</b>
            </Typography>
            <Typography variant="body2">Publish 2 posts</Typography>
          </Grid>
          <Grid item xs={12}>
            <LinearProgress variant="determinate" value={50} />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default RewardCard
