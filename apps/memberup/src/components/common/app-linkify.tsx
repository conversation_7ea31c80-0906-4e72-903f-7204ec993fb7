import { getURLDataApi } from '@memberup/shared/src/services/apis/unfurl.api'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Link from '@mui/material/Link'
import Typography from '@mui/material/Typography'
import useTheme from '@mui/material/styles/useTheme'
import Linkify from 'linkify-react'
import React, { useEffect, useState } from 'react'
import Skeleton from '@mui/material/Skeleton'

function RenderLink(props: { attributes: any; content: string; isMessage: boolean }) {
  const {
    content,
    isMessage,
    attributes: { href, saveFirstURL, ...rest },
  } = props
  const url = href.replace('</a>', '')
  saveFirstURL(url)

  // Check if the href is an email address
  const isEmail = /\S+@\S+\.\S+/.test(url)

  return isMessage && !isEmail ? (
    <Box />
  ) : (
    <Link href={url} target="_blank" underline="hover" {...rest}>
      {content}
    </Link>
  )
}

function RenderLinkDesription(props: { linkData: any; isMessage: boolean; theme: any }) {
  const { linkData, isMessage, theme } = props

  // Just render standard link
  const temp = linkData.url?.split('://')
  if (!temp) {
    return null
  }

  const host = temp?.[1]?.split('/')
  if (!host) {
    return null
  }

  if (isMessage) {
    return (
      <Box
        key={`link-preview-${host}`}
        sx={{
          mt: 0,
          maxWidth: {
            xs: 160,
            sm: 300,
          },
        }}
      >
        {Boolean(linkData.imageSrc) && (
          <Box
            sx={{
              position: 'relative',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <a href={linkData.url}>
              <picture>
                <img
                  src={linkData.imageSrc.url || linkData.imageSrc}
                  alt="link preview"
                  style={{ borderRadius: '16px', width: '100%', height: 'auto' }}
                />
              </picture>
            </a>
            {linkData.favicon?.indexOf('youtube.com') > 0 && (
              <picture style={{ position: 'absolute' }}>
                <img src={linkData.favicon} alt="link favicon" style={{ maxWidth: 68 }} />
              </picture>
            )}
          </Box>
        )}
        {Boolean(true) && (
          <Typography sx={{ mt: '14px', p: '0px 15px' }}>
            <Link
              className="font-family-graphik-semibold"
              href={host[0].includes('linkedin') ? linkData.url : `${temp[0]}://${host[0]}`}
              target="_blank"
              underline="hover"
              style={host[0].includes('linkedin') ? { marginLeft: '-14px' } : undefined}
            >
              {host[0].includes('linkedin') ? linkData.url : host[0]}
            </Link>
          </Typography>
        )}
        {Boolean(linkData.title) && (
          <Typography className="font-family-graphik-semibold" sx={{ mt: '8px', p: '0px 15px' }}>
            {linkData.title}
          </Typography>
        )}
        {Boolean(true) && (
          <Typography
            variant="body1"
            sx={{ mt: '12px', lineHeight: '20px', p: '0px 15px 15px 15px' }}
          >
            {linkData.description}
          </Typography>
        )}
      </Box>
    )
  }

  return (
    <Box
      key={`link-preview-${host}`}
      sx={{
        mt: 2,
      }}
    >
      <Grid container flexWrap="nowrap" spacing={2}>
        <Grid item>
          <Box
            sx={{
              borderRadius: 1,
              backgroundColor: theme.palette.text.disabled,
              opacity: 0.4,
              height: '100%',
              width: 4,
            }}
          />
        </Grid>
        <Grid item xs>
          {Boolean(linkData.title) && (
            <Typography variant="subtitle1" gutterBottom>
              <Link href={linkData.url} target="_blank" underline="hover">
                {linkData.title}
              </Link>
            </Typography>
          )}
          {Boolean(linkData.description) && (
            <Typography color="text.disabled" variant="body2" gutterBottom>
              {linkData.description}
            </Typography>
          )}
          {Boolean(linkData.imageSrc) && (
            <picture>
              <img src={linkData.imageSrc} alt="link preview" style={{ maxWidth: 300 }} />
            </picture>
          )}
        </Grid>
      </Grid>
    </Box>
  )
}

function AppLinkify(props: {
  content: string
  isMessage?: boolean
  visibleLinks?: boolean
  skeletonDimensions?: any
}) {
  const { content, isMessage, visibleLinks = true, skeletonDimensions = null } = props
  const theme = useTheme()
  const [firstURL, setFirstURL] = useState(null)
  const [urlData, setUrlData] = useState()
  const [isLoading, setIsLoading] = useState(false)
  const hasLink = /\b(\.[a-z]{2,4}\b)/i.test(content)

  useEffect(() => {
    if (!firstURL) return
    setIsLoading(true)
    getURLDataApi(firstURL)
      .then((res) => {
        if (res.data.success) {
          setUrlData(res.data.data)
        }
        setIsLoading(false)
      })
      .catch((err) => {
        setIsLoading(false)
      })
  }, [firstURL])

  // TODO: For youtube videos we don't need to actually Linkify, we will just show the video.
  // TODO: Define what to do when multiple video links are in the post. Should we show all? Should we just show the latest?
  return (
    <Box className="text-left">
      {isLoading && skeletonDimensions ? (
        <Skeleton
          variant="rounded"
          height={'100px'}
          width={'300px'}
          sx={{ borderRadius: '4px', bgcolor: 'rgba(0, 0, 0, 0.08)' }}
        />
      ) : (
        <>
          <Linkify
            options={{
              render: (props) => RenderLink({ ...props, isMessage }),
              attributes: {
                saveFirstURL: (url) => {
                  if (visibleLinks) {
                    if (!firstURL) {
                      setFirstURL(url)
                    }
                  }
                },
              },
            }}
          >
            <Typography
              className="pre-line"
              style={{
                opacity: 0.87,
                fontFamily: 'Graphik Regular',
                fontSize: '14px',
                lineHeight: '20px',
                padding: hasLink ? '10px' : '0px',
              }}
            >
              {content}
            </Typography>
          </Linkify>
          {urlData && (
            <RenderLinkDesription linkData={urlData} isMessage={isMessage} theme={theme} />
          )}
        </>
      )}
    </Box>
  )
}

AppLinkify.displayName = 'AppLinkfy'

export default React.memo(AppLinkify)
