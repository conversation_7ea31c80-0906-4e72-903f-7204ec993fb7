import useTheme from '@mui/material/styles/useTheme'
import { Helmet } from 'react-helmet'

import { cn } from '@/lib/utils'
import { THEME_MODE_ENUM } from '@/shared-types/enum'

export default function MUILayoutHelmet() {
  const theme = useTheme()

  return (
    <Helmet>
      <body className={cn('subpixel-antialiased', theme.palette.mode === THEME_MODE_ENUM.dark && 'dark')} />
    </Helmet>
  )
}
