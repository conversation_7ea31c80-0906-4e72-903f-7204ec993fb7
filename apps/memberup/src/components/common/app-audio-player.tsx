import AppAudioThumbnail from '@/memberup/components/common/app-audio-thumbnail'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import Box from '@mui/material/Box'
import React from 'react'
import AudioPlayer from 'react-h5-audio-player'

function AppAudioPlayer(props: {
  autoPlay?: boolean
  showJumpControls?: boolean
  showFilledProgress?: boolean
  thumbnailUrl?: string
  url: string
}) {
  const { autoPlay, showJumpControls, showFilledProgress, thumbnailUrl, url } = props

  return (
    <Box
      className="d-flex align-center justify-center"
      sx={{
        position: 'relative',
        height: '100%',
        overflow: 'hidden',
      }}
    >
      <Box
        sx={{
          width: '100%',
          overflow: 'hidden',
        }}
      >
        {thumbnailUrl ? (
          <Box
            className="app-image-wrapper"
            sx={{
              width: '100%',
              overflow: 'hidden',
            }}
          >
            <AppImg
              alt="Library Thumbnail"
              src={thumbnailUrl}
              width={235}
              height={140}
              style={{ width: '100%', height: 'auto' }}
            />
          </Box>
        ) : (
          <Box
            sx={{
              height: 192,
              width: '100%',
              overflow: 'hidden',
            }}
          >
            <AppAudioThumbnail />
          </Box>
        )}
        <AudioPlayer
          autoPlay={autoPlay}
          showJumpControls={showJumpControls}
          showFilledProgress={showFilledProgress}
          src={url}
        />
      </Box>
    </Box>
  )
}

AppAudioPlayer.displayName = 'AppAudioPlayer'

export default React.memo(AppAudioPlayer)
