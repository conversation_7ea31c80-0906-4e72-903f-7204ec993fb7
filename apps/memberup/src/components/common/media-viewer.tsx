'use client'

import Image from 'next/image'
import React, { useMemo } from 'react'

import useAppTheme from '../hooks/use-app-theme'
import { AppVideo } from '@memberup/shared/src/components/common/media/video'
import { MediaClickHandler, TAttachment } from '@memberup/shared/src/types/types'
import { GiphyGif } from '@/components/images/giphy-gif'
import { ImageNumberBadge } from '@/components/images/image-number-badge'
import { cn } from '@/lib/utils'

function MediaViewer({
  attachments,
  handleMediaClick,
}: {
  attachments: TAttachment[]
  handleMediaClick: MediaClickHandler
}) {
  const { isMobile } = useAppTheme()

  const displayedAttachments = useMemo(() => attachments?.slice(0, isMobile ? 3 : 4), [attachments, isMobile])

  if (attachments.length === 0) {
    return null
  }

  const imageWidth = 700
  const imageHeight = 700

  const singleAttachment = attachments.length === 1 ? attachments[0] : null

  const imageClassName = 'relative object-scale-down h-fit max-w-full max-h-80 md:max-h-[30rem] z-20'
  const blurredBackgroundClassName = 'blur-3xl absolute top-0 left-0 object-fill w-full h-full z-10'

  const galleryItemClassName = 'post-media-gallery-item aspect-square cursor-zoom-in'
  const galleryImageClassName = 'post-media-gallery-image object-cover w-full h-full max-h-full absolute z-10'

  return (
    <div className="media-viewer tailwind-component mt-4">
      {singleAttachment ? (
        <div className="relative w-full dark:bg-[#585d6614]">
          {singleAttachment.mimetype === 'video' && singleAttachment.url ? (
            <AppVideo url={singleAttachment.url} canPlay={true} height={393} />
          ) : (
            <div className="single-image-container relative flex max-h-80 items-center justify-center overflow-hidden rounded-xl md:max-h-[30rem]">
              {singleAttachment.mimetype === 'gif' ? (
                <>
                  <GiphyGif id={singleAttachment.id} className={imageClassName} width={imageWidth} />
                  <GiphyGif id={singleAttachment.id} className={blurredBackgroundClassName} width={imageWidth} />
                </>
              ) : (
                <>
                  <Image
                    onClick={() => handleMediaClick && handleMediaClick(0, attachments)}
                    width={imageWidth}
                    height={imageHeight}
                    id={singleAttachment.url}
                    src={singleAttachment.thumbnail || singleAttachment.url}
                    alt={`Attachment ${singleAttachment.filename}`}
                    className={imageClassName}
                    priority
                  />
                  <Image
                    onClick={() => handleMediaClick && handleMediaClick(0, attachments)}
                    src={singleAttachment.thumbnail || singleAttachment.url}
                    alt={`Blurred ${singleAttachment.filename}`}
                    className={blurredBackgroundClassName}
                    width={imageWidth}
                    height={imageHeight}
                    quality={20}
                    priority
                  />
                </>
              )}
            </div>
          )}
        </div>
      ) : (
        <div className={cn('media-viewer-gallery grid gap-2', isMobile ? 'grid-cols-3' : 'grid-cols-4')}>
          {displayedAttachments.map((attachment, index) => (
            <div className={cn('gallery-item', galleryItemClassName)} key={attachment.id || index}>
              <div className="gallery-item-image-container square">
                <div
                  className="gallery-item-image-wrapper absolute left-0 top-0 flex h-full w-full items-center justify-center overflow-hidden rounded bg-[#585d6614]"
                  onClick={() => handleMediaClick && handleMediaClick(index, attachments)}
                >
                  {attachment.mimetype === 'gif' ? (
                    <GiphyGif className={galleryImageClassName} id={attachment.id} width={imageWidth} />
                  ) : (
                    <Image
                      className={galleryImageClassName}
                      width={imageWidth}
                      height={imageHeight}
                      id={attachment.url}
                      src={attachment.thumbnail || attachment.url}
                      alt={`Attachment ${attachment.filename}`}
                      priority
                    />
                  )}
                  {index === displayedAttachments.length - 1 && displayedAttachments.length < attachments.length && (
                    <ImageNumberBadge number={attachments.length} />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default MediaViewer
