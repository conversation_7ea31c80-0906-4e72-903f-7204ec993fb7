import Box from '@mui/material/Box'
import { makeStyles } from '@mui/styles'

const useStyles = makeStyles((theme) => {
  return {
    videoContainer: {
      position: 'relative',
      width: '100%',
      paddingTop: '56.25%', // 16:9 aspect ratio
      overflow: 'hidden',
    },
    iframe: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      border: 0,
    },
  }
})

export default function MediaLinkYoutube({ videoId, startTime }) {
  const classes = useStyles()
  startTime = startTime || 0
  return (
    <Box className={classes.videoContainer}>
      <iframe
        className={classes.iframe}
        src={`https://www.youtube.com/embed/${videoId}?start=${startTime}`}
        allowFullScreen
        title="YouTube Video Player"
      ></iframe>
    </Box>
  )
}
