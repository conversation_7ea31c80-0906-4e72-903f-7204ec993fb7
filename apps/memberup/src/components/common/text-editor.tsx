import Box from '@mui/material/Box'
import useTheme from '@mui/material/styles/useTheme'
import dynamic from 'next/dynamic'
import React from 'react'

import 'react-quill/dist/quill.bubble.css'

import useAppTheme from '../hooks/use-app-theme'

const ReactQuill = dynamic(
  async () => {
    const { default: RQ } = await import('react-quill-new')
    const { default: QuillAutoDetectUrl } = await import('quill-auto-detect-url')
    RQ.Quill.register('modules/autoDetectUrl', QuillAutoDetectUrl)

    return import('react-quill-new')
  },
  { ssr: false },
)

const defaultModules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
    ['link', 'image'],
    // ['clean'],
  ],
  autoDetectUrl: true,
  clipboard: {
    matchVisual: false,
  },
}

const formats = ['header', 'bold', 'italic', 'underline', 'strike', 'blockquote', 'list', 'indent', 'link', 'image']

const AppTextEditor: React.FC<{
  defaultValue?: string
  placeholder?: string
  value?: string
  modules?: any
  sx?: any
  onChange: (e: string) => void
  editorTheme?: string
  disabled?: boolean
}> = ({ defaultValue, modules, placeholder, value, sx, onChange, editorTheme = 'bubble', disabled = false }) => {
  const theme = useTheme()
  const { isDarkTheme } = useAppTheme()

  return (
    <Box
      className="text-editor-container"
      sx={{
        width: '100%',
        height: '100%',
        position: 'relative',

        '& .quill': {
          height: '100%',
        },

        '& .ql-editor': {
          padding: '0px',
          overflow: 'visible',
        },
        '& .ql-container.ql-bubble:not(.ql-disabled) a': {
          whiteSpace: 'normal',
        },
        ...sx,
      }}
    >
      <style jsx global>{`
        .ql-container.ql-bubble:not(.ql-disabled) a::before {
          background-color: ${isDarkTheme ? '#151619' : '#F6F7F8'}!important;
          color: ${isDarkTheme ? '' : 'black'}!important;
          font-weight: 400 !important;
          border: 1px solid ${isDarkTheme ? '#151619' : theme.palette.divider}!important;
          -webkit-font-smoothing: antialiased !important;
        }

        .ql-container.ql-bubble:not(.ql-disabled) a::after {
          border-top: 6px solid ${isDarkTheme ? '#151619' : '#F6F7F8'}!important;
        }
      `}</style>
      <ReactQuill
        className="[&_.ql-editor]text-pure-black [&_.ql-editor]dark:text-pure-white break-words [&_.ql-editor]:font-sans [&_.ql-editor]:text-base [&_.ql-editor]:leading-7 [&_.ql-editor_*]:font-sans [&_.ql-editor_strong]:font-semibold"
        defaultValue={defaultValue}
        formats={formats}
        modules={modules || defaultModules}
        placeholder={placeholder}
        value={value}
        onChange={(e) => {
          onChange(e)
        }}
        theme={editorTheme}
        readOnly={disabled}
        onChangeSelection={() => {
          if (editorTheme != 'bubble') return
          const tooltipInput = document.querySelector('.ql-tooltip-editor input[type=text]')
          tooltipInput?.setAttribute('placeholder', 'https://google.com')
        }}
      />
    </Box>
  )
}

export default AppTextEditor
