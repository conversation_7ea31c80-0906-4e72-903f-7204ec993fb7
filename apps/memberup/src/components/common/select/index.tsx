import { Select as MUISelect, SelectProps } from '@mui/material'

import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import ArrowDown from '@/memberup/components/icons/arrow-down'

export default function Select({ sx, children, ...props }: SelectProps) {
  const { isDarkTheme } = useAppTheme()

  return (
    <MUISelect
      sx={{
        maxWidth: '340px',
        '& .MuiOutlinedInput-notchedOutline': {
          border: 'none',
        },
        '& .MuiSelect-select': {
          background: isDarkTheme ? 'var(--dark-background-3)' : 'var(--ui-light-300)',
          borderRadius: '12px',
          color: isDarkTheme ? 'var(--body-copy)' : 'var(--font-light-ui-gray)',
          fontSize: '14px',
          fontFamily: 'Graphik Semibold',
          height: '16px',
          lineHeight: '16px',
          minHeight: 'unset',
          padding: '12px 36px 12px 12px !important',
        },
        '& svg': {
          width: '15px',
          right: '12px',
          color: isDarkTheme ? 'var(--ui-dark-1000)' : 'var(--font-light-ui-gray)',
        },
        ...sx,
      }}
      IconComponent={ArrowDown}
      {...props}
    >
      {children}
    </MUISelect>
  )
}
