import { Box, Grid } from '@mui/material'

import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SkeletonListingItem from './skeleton-listing-item'

export default function SkeletonGrid() {
  const { isDarkTheme } = useAppTheme()

  return (
    <Box
      className="rounded-container padded bg-pure-white dark:bg-dark-background-3"
      sx={{
        position: 'relative',
        padding: {
          lg: '32px 39px 28px !important',
          xl: '32px 59px 28px !important',
        },
        '&:after': {
          content: '""',
          display: 'block',
          position: 'absolute',
          left: 0,
          top: 0,
          width: '100%',
          height: '100%',
          background: isDarkTheme
            ? 'rgb(var(--surface-1-rgb), 0.6)'
            : 'rgb(var(--pure-white), 0.6)',
          backdropFilter: 'blur(5.5px)',
        },
      }}
    >
      <Grid container spacing={{ xs: 3, lg: 4 }}>
        <Grid xs={12} lg={6} item>
          <SkeletonListingItem />
        </Grid>
        <Grid xs={12} lg={6} item sx={{ position: 'relative', left: { lg: '-20px' } }}>
          <SkeletonListingItem />
        </Grid>
        <Grid xs={12} lg={6} item>
          <SkeletonListingItem />
        </Grid>
        <Grid xs={12} lg={6} item sx={{ position: 'relative', left: { lg: '-20px' } }}>
          <SkeletonListingItem />
        </Grid>
      </Grid>
    </Box>
  )
}
