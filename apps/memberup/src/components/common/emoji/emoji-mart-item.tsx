import data from '@emoji-mart/data/sets/14/facebook.json'
import { init } from 'emoji-mart'
import * as React from 'react'

init({
  data,
})

declare global {
  namespace JSX {
    interface IntrinsicElements {
      'em-emoji': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement> & {
        id?: string
        shortcodes?: string
        native?: string
        size?: string | number
        fallback?: string
        set?: 'native' | 'apple' | 'facebook' | 'google' | 'twitter'
        skin?: string | number
      }
    }
  }
}

export const EmojiMartItem = ({ set, emoji, size }) => {
  return <em-emoji set={set} id={emoji} size={size} />
}
