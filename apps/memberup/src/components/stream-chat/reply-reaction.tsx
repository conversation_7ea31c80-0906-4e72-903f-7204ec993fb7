import useAppStreamReactionListener from '@/memberup/components/hooks/use-app-stream-reaction-listener'
import SVGCommentIcon from '@/memberup/components/svgs/comment'
import SVGFavoriteIcon from '@/memberup/components/svgs/favorite'
import SVGFavoriteBorderIcon from '@/memberup/components/svgs/favorite-border'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { getLikesStr } from '@memberup/shared/src/libs/string-utils'
import { knockApi } from '@memberup/shared/src/services/apis/knock.api'
import { KNOCK_WORKFLOW_ENUM } from '@memberup/shared/src/types/enum'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import clsx from 'clsx'
import React, { useMemo, useState } from 'react'
import { StreamMessage, useChannelStateContext } from 'stream-chat-react'

const ReplyReaction: React.FC<{
  message: StreamMessage
  enableNewComment?: boolean
  enableLike?: boolean
  fontSize?: number
  onNewCommentClick: any
}> = ({
  message: originalMessage,
  enableNewComment = true,
  enableLike = true,
  fontSize = 14,
  onNewCommentClick,
}) => {
  const mountedRef = useMounted(true)
  const membership = useAppSelector((state) => selectMembership(state))
  const user = useAppSelector((state) => selectUser(state))
  const theme = useTheme()
  const { channel: streamChatChannel } = useChannelStateContext()
  const [requestReaction, setRequestReaction] = useState(false)

  const { reactions, updatedMessage } = useAppStreamReactionListener(
    streamChatChannel,
    originalMessage
  )
  const message = updatedMessage || originalMessage
  const liked = reactions.find(
    (item) => item.type === 'like' && (item.user_id || item.user?.id) === user?.id
  )

  const handleReaction = (reactionType: string) => {
    setRequestReaction(true)
    if (liked) {
      streamChatChannel
        .deleteReaction(message.id, reactionType)
        .then((res) => {})
        .catch((err) => {})
        .finally(() => {
          if (mountedRef.current) {
            setRequestReaction(false)
          }
        })
    } else {
      streamChatChannel
        .sendReaction(message.id, {
          type: 'like',
        })
        .then((res) => {
          if (message.user?.id !== (res.reaction.user?.id || res.reaction.user_id)) {
            knockApi({
              workflow_key: KNOCK_WORKFLOW_ENUM.new_like,
              data: {
                id: res.reaction.id,
                community_name: membership.name,
                snippet_of_post: `liked your ${message.parent_id ? 'comment' : 'post'}.`,
              },
              recipients: [message.user.id],
            })
          }
        })
        .catch((err) => {})
        .finally(() => {
          if (mountedRef.current) {
            setRequestReaction(false)
          }
        })
    }
  }

  const renderLikedByUsersStr = useMemo(() => {
    if (!message.reaction_counts.like) return null
    return (
      <Grid
        className="font-family-graphik-semibold text-right"
        style={{ fontSize: '12px' }}
        item
        xs
      >
        {Boolean(message.reaction_counts?.like) && getLikesStr(null, message.reaction_counts.like)}
      </Grid>
    )
  }, [message])

  return (
    <Box
      sx={{ fontSize: `${fontSize}px`, color: theme.palette.mode === 'light' ? 'black' : 'white' }}
    >
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Grid container spacing={2} alignItems="center" sx={{ mt: 2 }}>
            {enableLike && (
              <Grid item>
                <span
                  className={clsx('reaction-button', {
                    'no-pointer': requestReaction,
                  })}
                  onClick={() => handleReaction('like')}
                >
                  {liked ? (
                    <SVGFavoriteIcon width={18} height={16} data-cy="liked" />
                  ) : (
                    <SVGFavoriteBorderIcon width={18} height={16} data-cy="not-liked" />
                  )}
                </span>
              </Grid>
            )}
            {enableNewComment && (
              <Grid
                item
                onClick={() => onNewCommentClick()}
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    fontWeight: 500,
                  },
                }}
                data-cy="spark-comment-btn"
              >
                <Grid container alignItems="center">
                  <Grid item sx={{ ml: 2 }}>
                    <SVGCommentIcon fontSize={fontSize + 2} />
                  </Grid>
                </Grid>
              </Grid>
            )}
            {renderLikedByUsersStr}
          </Grid>
        </Grid>
      </Grid>
    </Box>
  )
}

export default ReplyReaction
