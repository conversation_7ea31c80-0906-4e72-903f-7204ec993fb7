import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { checkAdminOrCreatorRole } from '@memberup/shared/src/libs/profile'
import { useMemo } from 'react'
import { getFullName } from '@/shared-libs/profile'

const useCheckUserRole = () => {
  const membership = useAppSelector((state) => selectMembership(state))
  const user = useAppSelector((state) => selectUser(state))
  const isAdminOrCreator = useMemo(
    () => checkAdminOrCreatorRole(user?.role, user?.membership_id, membership?.id),
    [membership?.id, user?.membership_id, user?.role]
  )

  const userFullName = getFullName(user?.first_name, user?.last_name, '')

  return { isAdminOrCreator, user, userFullName }
}

export default useCheckUserRole
