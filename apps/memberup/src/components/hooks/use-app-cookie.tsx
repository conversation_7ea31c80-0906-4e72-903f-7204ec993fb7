import { getLocationInfo } from '@/memberup/libs/utils'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import axios from 'axios'
import Cookie from 'js-cookie'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN

const useAppCookie = () => {
  const membership = useAppSelector((state) => selectMembership(state))

  const deleteAppCookie = (name?: string, options?: Cookie.CookieAttributes) => {
    if (name || membership?.slug) {
      Cookie.remove(name || `${membership.slug}-auth-token`, options)
      Cookie.remove(name || `${membership.slug}-auth-token`, { domain: `.${DEFAULT_DOMAIN}` })
    }
  }

  const getAppCookie = (name?: string) => {
    if (name || membership?.slug) {
      return Cookie.get(name || `${membership.slug}-auth-token`)
    }
    return ''
  }

  const updateAppCookie = (value: string, name?: string, options?: Cookie.CookieAttributes) => {
    if (name) {
      Cookie.set(name, value, options)
    } else if (membership?.slug) {
      const { domain, isLocalhost } = getLocationInfo()
      Cookie.set(
        `${membership.slug}-auth-token`,
        value,
        options || { domain: isLocalhost ? 'localhost.com' : domain }
      )
    }
  }

  const updateAppCookieAuthToken = (email: string, password: string, redirectTo?: string) => {
    axios
      .post('/api/json-encode', {
        email,
        password,
        redirectTo: redirectTo || 'home',
      })
      .then((res) => {
        updateAppCookie(res.data.token)
      })
      .catch(() => {})
  }
  return { deleteAppCookie, getAppCookie, updateAppCookie, updateAppCookieAuthToken }
}

export default useAppCookie
