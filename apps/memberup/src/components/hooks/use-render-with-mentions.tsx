import RenderMention from '../common/render-mention'
import parse from 'html-react-parser'
import { stripHtml } from '@/shared-libs/string-utils'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import { makeStyles, useTheme } from '@mui/styles'
import { useMemo } from 'react'
import { unescapeSlashesAndAddLinks } from '@/memberup/libs/utils'

const useStyles = makeStyles((theme) => ({
  player: {
    borderRadius: '16px',
    overflow: 'hidden',
    marginBottom: '8px',
  },
}))

const EVERYONE_USER_ID = '-1'

const renderReactElements = (htmlString, members) => {
  const replaceMentionWithComponent = (node) => {
    if (node.attribs && node.attribs['data-type']) {
      const dataType = node.attribs['data-type']
      if (dataType === 'mention') {
        const userId = node.attribs['data-user-id']
        const isEveryone = userId === EVERYONE_USER_ID
        let member = members[userId] || {
          first_name: node.attribs['data-user-name'],
          last_name: '',
        }
        return <RenderMention isEveryone={isEveryone} userData={member} viewType={'single'} />
      }
    }
  }
  const reactElements = parse(htmlString, { replace: replaceMentionWithComponent })

  return reactElements
}

export function useRenderTextWithMentions(
  text,
  mentionedUsers = [],
  members: { [key: string]: IUser },
  viewType: 'condensed' | 'single' | 'comment' = 'single',
  feed: IFeed
) {
  const classes = useStyles()
  const feedId = feed?.id
  const theme = useTheme()
  const primaryColor = theme.palette.primary.main
  return useMemo(() => {
    if (viewType === 'condensed') {
      return <>{stripHtml(feed.text)}</>
    }

    if (viewType === 'single') {
      return renderReactElements(feed.text, members)
    }

    // NOTE: In the case of comments as we don't have a way to edit links yet, we just detect links and render them.
    if (viewType === 'comment') {
      return renderReactElements(unescapeSlashesAndAddLinks(feed.text, primaryColor), members)
    }
  }, [text, mentionedUsers, members])
}
