import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import _startCase from 'lodash/startCase'
import Image from 'next/image'

const PostSearchResultItem = ({ item, onClickSearchResultHandler }) => {
  const title = item.title || ''
  const content = item.text || ''
  const author = item.author_full_name || ''
  const createdAt = item.createdAt
  const feedID = item.objectID
  const permalink = item.permalink || feedID
  const spaceName = item.channel
  const postPicURL = item.post_pic_url
  const formattedContent = content.substring(0, 200) + (content.length > 200 ? '...' : '')

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
        borderRadius: 1,
        transition: 'all 0.3s ease',
        boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.01)',
      }}
      onClick={() => onClickSearchResultHandler(permalink)}
    >
      <Typography sx={{ color: '#2F2F2F', fontSize: '0.8rem', marginBottom: '6px' }}>
        <span style={{ fontWeight: 'bold' }}>{author}</span> posted in{' '}
        <span style={{ fontWeight: 'bold' }}>{spaceName}</span>{' '}
        {getDateTimeFromNow(createdAt, true)}
      </Typography>
      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4, marginRight: '20px' }}>
          <Typography
            variant="h6"
            sx={{ color: '#000000', fontFamily: 'Graphik Semibold', fontSize: '14px' }}
          >
            {_startCase(title)}
          </Typography>
          <div style={{ wordWrap: 'break-word' }}>
            <Typography
              variant="body2"
              sx={{ color: '#2d2d2d', fontSize: '0.82rem', wordBreak: 'break-all' }}
            >
              {formattedContent}
            </Typography>
          </div>
        </div>
        <div>
          {postPicURL ? (
            postPicURL?.includes('giphy.com') ? (
              <div
                style={{
                  borderRadius: '10px',
                  overflow: 'hidden',
                  width: '45px',
                  height: '45px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#000', // Set to the desired background color
                }}
              >
                <iframe
                  src={postPicURL}
                  style={{
                    transform: 'scale(1.4)', // Adjust scale as needed
                    transformOrigin: 'center',
                    borderRadius: '10px',
                    pointerEvents: 'none',
                  }}
                  width="45" // Adjust width as needed
                  height="45" // Adjust height as needed
                  frameBorder="0"
                ></iframe>
              </div>
            ) : (
              <Image
                style={{ borderRadius: '10px' }}
                src={postPicURL}
                alt="Post Image"
                width={45}
                height={45}
              />
            )
          ) : null}
        </div>
      </div>
    </Box>
  )
}

export default PostSearchResultItem
