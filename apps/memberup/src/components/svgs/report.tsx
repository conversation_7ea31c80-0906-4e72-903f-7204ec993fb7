import React from 'react'

const SVGReport: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 16}
      height={height || 16}
      viewBox="0 0 16 16"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -2.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(2.000000, 2.000000)">
            <path d="M11.2,0 C11.4121732,0 11.6156563,0.08428547 11.7656854,0.23431458 L15.7656854,4.23431458 C15.9157145,4.38434368 16,4.58782681 16,4.8 L16,10.4 C16,10.5871492 15.934387,10.7683755 15.814577,10.9121475 L11.814577,15.7121475 C11.662582,15.8945416 11.4374239,16 11.2,16 L4.8,16 C4.58782681,16 4.38434368,15.9157145 4.23431458,15.7656854 L0.23431458,11.7656854 C0.08428547,11.6156563 0,11.4121732 0,11.2 L0,4.8 C0,4.58782681 0.08428547,4.38434368 0.23431458,4.23431458 L4.23431458,0.23431458 C4.38434368,0.08428547 4.58782681,0 4.8,0 L11.2,0 Z M8,11 C7.44771525,11 7,11.4477153 7,12 C7,12.5522847 7.44771525,13 8,13 C8.5522847,13 9,12.5522847 9,12 C9,11.4477153 8.5522847,11 8,11 Z M8,3 C7.48716416,3 7.06449284,3.38604019 7.00672773,3.88337887 L7,4 L7,8 C7,8.5522847 7.44771525,9 8,9 C8.5128358,9 8.9355072,8.6139598 8.9932723,8.1166211 L9,8 L9,4 C9,3.44771525 8.5522847,3 8,3 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGReport
