import React from 'react'

const <PERSON><PERSON>parkle: React.FC<{ width?: number; height?: number; styles?: any }> = ({ width, height, styles }) => {
  return (
    <svg width={width || '12px'} height={height || '12px'} style={{ ...styles }} viewBox="0 0 12 12" version="1.1">
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/sparkle" transform="translate(-2, -2)" fill="currentColor">
          <path
            d="M8.65505881,2.5428022 C9.11569128,4.978863 11.021137,6.88430872 13.4571978,7.34494119 C14.1809341,7.48179181 14.1809341,8.51820819 13.4571978,8.65505881 C11.021137,9.11569128 9.11569128,11.021137 8.65505881,13.4571978 C8.51820819,14.1809341 7.48179181,14.1809341 7.34494119,13.4571978 C6.88430872,11.021137 4.978863,9.11569128 2.5428022,8.65505881 C1.81906593,8.51820819 1.81906593,7.48179181 2.5428022,7.34494119 C4.978863,6.88430872 6.88430872,4.978863 7.34494119,2.5428022 C7.48179181,1.81906593 8.51820819,1.81906593 8.65505881,2.5428022 Z"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGSparkle
