import React from 'react'

const SVGChevronRight: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg
      width={width || '24px'}
      height={height || '24px'}
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="🏠-Home---Posts---Detail" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="🌑-Post---Detail---Image-Max---Modal"
          transform="translate(-1376, -394)"
          fill="currentColor"
        >
          <g id="🌑-Dark/-Button--/icon/big/1.-enabled" transform="translate(1368, 386)">
            <g id="Icons/system/24px/close" transform="translate(15, 11)">
              <path
                d="M9.00025,18.00025 C8.74425,18.00025 8.48825,17.90225 8.29325,17.70725 L0.29325,9.70725 C-0.09775,9.31625 -0.09775,8.68425 0.29325,8.29325 L8.29325,0.29325 C8.68425,-0.09775 9.31625,-0.09775 9.70725,0.29325 C10.09825,0.68425 10.09825,1.31625 9.70725,1.70725 L2.41425,9.00025 L9.70725,16.29325 C10.09825,16.68425 10.09825,17.31625 9.70725,17.70725 C9.51225,17.90225 9.25625,18.00025 9.00025,18.00025"
                id="Path"
                transform="translate(5.0002, 9.0001) scale(-1, 1) translate(-5.0002, -9.0001)"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGChevronRight
