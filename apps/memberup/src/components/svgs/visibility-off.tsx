import React from 'react'

const SVGVisibilityOff: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 16}
      height={height || 13}
      viewBox="0 0 16 13"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2.000000, -3.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(2.000000, 3.000000)">
            <path d="M2.63602622,0.56523965 L6.6220396,4.55043417 C6.98074894,4.20933684 7.46592785,4 8,4 C9.1045695,4 10,4.8954305 10,6 C10,6.5340722 9.7906632,7.0192511 9.4495658,7.3779604 L13.3639599,11.2929279 C13.7244403,11.6534001 13.7521757,12.2206187 13.4471618,12.6129051 L13.363968,12.7071195 C12.9734462,13.0976412 12.3402864,13.0976449 11.9497602,12.7071276 L10.7459001,11.5059932 C9.8918139,11.8340367 8.9758409,12 8,12 C4.80071316,12 2.24491753,10.2161416 0.40375204,6.7852602 L0.23323859,6.458794 L0,6 L0.23323859,5.54120598 C0.7732827,4.47890747 1.37811074,3.56506836 2.04579001,2.80340618 L1.22182655,1.97943932 C0.83130609,1.5889278 0.83129885,0.95577628 1.22181037,0.56525583 C1.61234023,0.17472597 2.24549999,0.17472235 2.63602622,0.56523965 Z M2.43738635,5.67716636 L2.25320687,6 L2.41039841,6.2768225 C3.89056825,8.8122964 5.73671394,10 8,10 C8.3904592,10 8.7685032,9.9646507 9.1343947,9.8931303 L3.46177381,4.21919632 C3.10252729,4.64220578 2.76124184,5.12764194 2.43738635,5.67716636 Z M8,0 C11.1992868,0 13.7550825,1.7838584 15.596248,5.21473976 L15.7667614,5.54120598 L16,6 L15.7667614,6.458794 C15.244603,7.4859103 14.6618807,8.374238 14.0203416,9.1204168 L12.6026563,7.7040361 C13.0067366,7.2168896 13.387859,6.64968 13.7467931,6 C12.2407672,3.27405445 10.3441177,2 8,2 C7.65185669,2 7.31358343,2.02810252 6.984994,2.08489014 L5.35623694,0.45565834 C6.18109634,0.15303218 7.06294652,0 8,0 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGVisibilityOff
