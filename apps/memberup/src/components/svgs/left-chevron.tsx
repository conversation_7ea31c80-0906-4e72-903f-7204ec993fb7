import React from 'react'

const SVGLeftChevron: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width,
  height,
  styles,
}) => {
  return (
    <svg
      width={'10px' || width}
      height={'18px' || height}
      viewBox="0 0 10 18"
      style={{ ...styles }}
    >
      <g id="🔶-Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Icons/system/24px/chevron-left" transform="translate(-7, -3)" fill="currentColor">
          <path
            d="M16.00025,21.00025 C15.74425,21.00025 15.48825,20.90225 15.29325,20.70725 L7.29325,12.70725 C6.90225,12.31625 6.90225,11.68425 7.29325,11.29325 L15.29325,3.29325 C15.68425,2.90225 16.31625,2.90225 16.70725,3.29325 C17.09825,3.68425 17.09825,4.31625 16.70725,4.70725 L9.41425,12.00025 L16.70725,19.29325 C17.09825,19.68425 17.09825,20.31625 16.70725,20.70725 C16.51225,20.90225 16.25625,21.00025 16.00025,21.00025"
            id="Path"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGLeftChevron
