import React from 'react'

const SVGBranding: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 14}
      height={height || 14}
      viewBox="0 0 14 14"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-3.000000, -3.000000)" fill="#8D94A3" fillRule="nonzero">
          <g transform="translate(3.000000, 3.000000)">
            <path d="M10.6159329,0.20970461 L10.7101403,0.29289322 L13.7101403,3.29289322 C14.0706242,3.65337718 14.0983538,4.22060824 13.7933289,4.61289944 L13.7101403,4.70710678 L4.7071068,13.7101403 C4.5508265,13.8664206 4.3481451,13.9656318 4.131444,13.9943611 L4,14.0030335 L1,14.0030335 C0.48716416,14.0030335 0.06449284,13.6169933 0.00672773,13.1196546 L-2.49578136e-13,13.0030335 L-2.49578136e-13,10.0030335 C-2.49578136e-13,9.78201974 0.07316447,9.56854945 0.20608063,9.39500397 L0.29289322,9.2959267 L9.29592668,0.29289322 C9.65641068,-0.06759074 10.2236417,-0.09532028 10.6159329,0.20970461 Z M10.0030335,2.41421356 L2,10.4172471 L2,12.0030335 L3.58578644,12.0030335 L11.5888199,4 L10.0030335,2.41421356 Z"></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGBranding
