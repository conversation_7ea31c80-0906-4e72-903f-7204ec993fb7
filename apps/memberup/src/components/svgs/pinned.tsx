import React from 'react'

const SV<PERSON>ushPinHeader: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width,
  height,
  styles,
}) => {
  return (
    <svg
      width={'10px' || width}
      height={'12px' || height}
      style={{ ...styles }}
      viewBox="0 0 10 12"
    >
      <g
        id="🟣-Home-Feed---Hidden-Pinned-Post"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="🌑-Home---Member-View-V1A" transform="translate(-727, -226)" fill="currentColor">
          <g id="sort-copy-5" transform="translate(368, 204)">
            <g
              id="🌑-Dark/-Modules-/-switches-/-toggle-icon-button-/-large-/-1.-enabled-Copy"
              transform="translate(344, 8)"
            >
              <g id="Group-16" transform="translate(15, 14)">
                <path
                  d="M8,0 C8.55228475,0 9,0.44771525 9,1 C9,1.55228475 8.55228475,2 8,2 L7,2 L7,6 L8.5,6 C9.32842712,6 10,6.67157288 10,7.5 L10,8 L6,8 L6,11 C6,11.5522847 5.55228475,12 5,12 C4.44771525,12 4,11.5522847 4,11 L4,8 L0,8 L0,7.5 C0,6.67157288 0.671572875,6 1.5,6 L3,6 L3,2 L2,2 C1.44771525,2 1,1.55228475 1,1 C1,0.44771525 1.44771525,0 2,0 L8,0 Z"
                  id="Path-Copy"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGPushPinHeader
