import React from 'react'

const SVGNotification: React.FC<{ fontSize?: number; viewBox?: string }> = ({
  fontSize,
  viewBox,
}) => {
  return (
    <svg
      width="1em"
      height="1em"
      fontSize={fontSize || '1.5rem'}
      viewBox={viewBox || '0 0 20 16'}
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-3.000000, -2.000000)" fill="currentColor" fillRule="nonzero">
          <g transform="translate(3.000000, 2.000000)">
            <path
              d="M1.00168062,14 C0.203120118,14 -0.273298602,13.110122 0.169465168,12.4455476 L0.169465168,12.4455476 L0.227591578,12.3583017 C1.38362528,10.6231318 2.00065123,8.5848165 2.00103553,6.49981568 L2.00103553,6.49981568 L2.00597205,6.28797781 C2.10922006,4.06855497 3.81996247,2.26923356 6.0004009,2.02760224 L6.00039041,1 C6.00039041,0.44771525 6.44810566,0 7.00039041,0 C7.55267511,0 8.00039041,0.44771525 8.00039041,1 L8.00071451,2.02750023 C10.2505337,2.27636972 12.0003904,4.18383439 12.0003904,6.5 L12.0003904,6.5 L12.0067831,6.86733315 C12.0748877,8.8233002 12.6857997,10.724513 13.774593,12.3566501 L13.774593,12.3566501 L13.8335657,12.4450521 C14.2768749,13.1095871 13.8005108,14 13.0016806,14 L13.0016806,14 L9.00039041,14 C9.00039041,15.1045695 8.10495991,16 7.00039041,16 C5.89582091,16 5.00039041,15.1045695 5.00039041,14 L1.00168062,14 Z M7.50039041,4 L6.50168068,4 C5.12079245,4 4.00129002,5.11929611 4.0010355,6.50018432 C4.00070543,8.2909505 3.61787104,10.0527574 2.88731203,11.6707122 L2.88731203,11.6707122 L2.73139041,12 L11.2703904,12 L11.1146242,11.6704725 C10.4396794,10.1771881 10.0613802,8.5610654 10.0071741,6.91270837 L10.0071741,6.91270837 L10.0003904,6.5 C10.0003904,5.11928813 8.88110231,4 7.50039041,4 L7.50039041,4 Z"
              id="Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGNotification
