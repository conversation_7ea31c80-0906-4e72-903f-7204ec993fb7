import React from 'react'

const SVGHidePost: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width,
  height,
  styles,
}) => {
  return (
    <svg
      width={'13px' || width}
      height={'11px' || height}
      viewBox="0 0 13 11"
      style={{ ...styles }}
    >
      <g
        id="🟣-Home-Feed---Hidden-Pinned-Post"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <g id="🌑-Home---Member-View-V1A" transform="translate(-948, -377)" fill="currentColor">
          <g
            id="🌑-Dark/Feed/desktop/list-pin/title+description/1.-enabled-Copy-8"
            transform="translate(368, 366)"
          >
            <g id="Icon-+-Text" transform="translate(559.5001, 8)">
              <path
                d="M22.757275,3.29288536 L31.2425563,11.777939 C31.6330806,12.1684528 31.6330806,12.8016008 31.2425563,13.1921146 C30.8520321,13.5826285 30.2188671,13.5826285 29.8283428,13.1921146 L28.0818476,11.4466992 C27.7318201,11.5059767 27.3712452,11.5353049 26.9999157,11.5353049 C24.636908,11.5353049 22.7094201,10.3476332 21.1640331,7.81222726 L20.9999157,7.53541219 C21.4126841,6.81983929 21.8535574,6.20431684 22.3236091,5.68562784 L21.3430614,4.70706097 C20.9525371,4.31654716 20.9525371,3.68339917 21.3430614,3.29288536 C21.7335857,2.90237155 22.3667507,2.90237155 22.757275,3.29288536 Z M26.9999157,3.53551953 C29.4473165,3.53551953 31.4275334,4.80953979 32.9999157,7.53541219 C32.5872114,8.25087385 32.146411,8.86631614 31.6764414,9.38495461 L25.9171809,3.62426122 C26.2674599,3.56489258 26.6283022,3.53551953 26.9999157,3.53551953 Z"
                id="Icon-Copy-2"
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGHidePost
