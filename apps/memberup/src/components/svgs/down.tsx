import React from 'react'

const SVGDown: React.FC<{ width?: number; height?: number; strokeWidth?: number; style?: any }> = ({
  width,
  height,
  style,
}) => {
  return (
    <svg
      style={style}
      width={width || '20px'}
      height={height || '20px'}
      viewBox="0 0 20 20"
      version="1.1"
    >
      <g
        id="Icons/system/20px/chevron-down"
        stroke="none"
        strokeWidth="1"
        fill="none"
        fillRule="evenodd"
      >
        <path
          d="M16.70725,7.70725 L10.70725,13.70725 C10.31625,14.09825 9.68425,14.09825 9.29325,13.70725 L3.29325,7.70725 C2.90225,7.31625 2.90225,6.68425 3.29325,6.29325 C3.68425,5.90225 4.31625,5.90225 4.70725,6.29325 L10.00025,11.58625 L15.29325,6.29325 C15.68425,5.90225 16.31625,5.90225 16.70725,6.29325 C16.90225,6.48825 17.00025,6.74425 17.00025,7.00025 C17.00025,7.25625 16.90225,7.51225 16.70725,7.70725 Z"
          id="Path"
          fill="currentColor"
        ></path>
      </g>
    </svg>
  )
}

export default SVGDown
