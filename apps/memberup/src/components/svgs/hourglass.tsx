import React from 'react'

const SVGHourglass: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width,
  height,
  styles,
}) => {
  return (
    <svg
      width={width || '8px'}
      height={height || '12px'}
      style={{ ...styles }}
      viewBox="0 0 8 12"
      version="1.1"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/system/16px/hourglass-empty" transform="translate(-4, -2)" fill="currentColor">
          <path
            d="M10,2 C11.0543618,2 11.9181651,2.81587779 11.9945143,3.85073766 L12,4 L12,6 C12,6.29255904 11.8720632,6.56818103 11.6537076,6.75676287 L11.5547002,6.83205029 L9.803,8 L11.5547002,9.16794971 C11.8328989,9.35341548 12,9.66564681 12,10 L12,12 C12,13.1045695 11.1045695,14 10,14 L6,14 C4.8954305,14 4,13.1045695 4,12 L4,10 C4,9.66564681 4.16710114,9.35341548 4.4452998,9.16794971 L6.196,8 L4.4452998,6.83205029 C4.20187597,6.66976774 4.04351118,6.41043031 4.00772348,6.1241411 L4,6 L4,4 C4,2.9456382 4.81587779,2.08183488 5.85073766,2.00548574 L6,2 L10,2 Z M8,9.203 L6,10.536 L6,12 L10,12 L10,10.536 L8,9.203 Z M10,4 L6,4 L6,5.464 L8,6.797 L10,5.464 L10,4 Z"
            id="Icon"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGHourglass
