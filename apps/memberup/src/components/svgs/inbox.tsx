import React from 'react'

const SVGInbox: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 20}
      height={height || 15}
      viewBox="0 0 20 15"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-6.000000, -9.000000)" stroke="#8D94A3" strokeWidth="2">
          <g transform="translate(6.000000, 9.000000)">
            <path
              d="M17,1 C17.5522847,1 18.0522847,1.22385763 18.4142136,1.58578644 C18.7761424,1.94771525 19,2.44771525 19,3 L19,3 L19,12 C19,12.5522847 18.7761424,13.0522847 18.4142136,13.4142136 C18.0522847,13.7761424 17.5522847,14 17,14 L17,14 L3,14 C2.44771525,14 1.94771525,13.7761424 1.58578644,13.4142136 C1.22385763,13.0522847 1,12.5522847 1,12 L1,12 L1,3 C1,2.44771525 1.22385763,1.94771525 1.58578644,1.58578644 C1.94771525,1.22385763 2.44771525,1 3,1 L3,1 Z"
              id="Rectangle"
            ></path>
            <path
              d="M19,3 L10.4856429,7.73019837 C10.1836204,7.89798867 9.81637961,7.89798867 9.51435707,7.73019837 L1,3 L1,3"
              id="Path"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGInbox
