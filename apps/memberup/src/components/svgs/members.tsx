import React from 'react'

const SVGMembers: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 16}
      height={height || 14}
      viewBox="0 0 16 14"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          id="Icon-/-20px-/-users"
          transform="translate(-2.000000, -3.000000)"
          fill="currentColor"
          fillRule="nonzero"
        >
          <g id="ic20-users" transform="translate(2.000000, 3.000000)">
            <path
              d="M5.5,7 C6.8739019,7 8.1301441,7.5129203 9.0940916,8.3609507 C9.6809009,8.1284735 10.324822,8 11,8 C13.7614237,8 16,10.1490332 16,12.8 C16,13.4627417 15.4403559,14 14.75,14 L1.375,14 C0.61560847,14 0,13.3731986 0,12.6 C0,9.5072054 2.46243388,7 5.5,7 Z M5,0 C6.65685425,0 8,1.34314575 8,3 C8,4.65685425 6.65685425,6 5,6 C3.34314575,6 2,4.65685425 2,3 C2,1.34314575 3.34314575,0 5,0 Z M12,2 C13.1045695,2 14,2.8954305 14,4 C14,5.1045695 13.1045695,6 12,6 C10.8954305,6 10,5.1045695 10,4 C10,2.8954305 10.8954305,2 12,2 Z"
              id="Shape"
            ></path>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGMembers
