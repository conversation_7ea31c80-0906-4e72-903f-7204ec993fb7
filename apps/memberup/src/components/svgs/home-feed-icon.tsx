import React from 'react'

const SVGHomeFeedIcon: React.FC<{ width?: number; height?: number; styles?: any }> = ({
  width,
  height,
  styles,
}) => {
  return (
    <svg
      width={'24px' || width}
      height={'24px' || height}
      viewBox="0 0 24 24"
      style={{ ...styles }}
    >
      <g id="🔶-Symbols" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g id="Icons/feature/-32px-/-home-feed" transform="translate(-4, -4)" fill="currentColor">
          <path
            d="M23,4 C25.7614237,4 28,6.23857625 28,9 L28,23 C28,25.7614237 25.7614237,28 23,28 L9,28 C6.23857625,28 4,25.7614237 4,23 L4,9 C4,6.23857625 6.23857625,4 9,4 L23,4 Z M23,6 L9,6 C7.34314575,6 6,7.34314575 6,9 L6,23 C6,24.6568542 7.34314575,26 9,26 L23,26 C24.6568542,26 26,24.6568542 26,23 L26,9 C26,7.34314575 24.6568542,6 23,6 Z M23,22 C23.5522847,22 24,22.4477153 24,23 C24,23.5522847 23.5522847,24 23,24 L9,24 C8.44771525,24 8,23.5522847 8,23 C8,22.4477153 8.44771525,22 9,22 L23,22 Z M10,8 C11.1045695,8 12,8.8954305 12,10 C12,11.1045695 11.1045695,12 10,12 C8.8954305,12 8,11.1045695 8,10 C8,8.8954305 8.8954305,8 10,8 Z"
            id="home-feed"
          ></path>
        </g>
      </g>
    </svg>
  )
}

export default SVGHomeFeedIcon
