import React from 'react'

const SVGModeration: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg
      width={width || 33}
      height={height || 40}
      viewBox="0 0 33 40"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g
          transform="translate(-1066.000000, -368.000000)"
          stroke="currentColor"
          strokeWidth={strokeWidth || 2}
        >
          <g transform="translate(758.000000, 88.000000)">
            <g transform="translate(308.000000, 280.000000)">
              <path d="M16.5,1.06715482 L32,6.84221856 L32,19.0220732 C32,23.772522 30.3744621,28.2144382 27.5947431,31.7485437 C24.858118,35.2278603 21.0028207,37.8272953 16.4784968,38.9747363 C11.9596198,38.021709 8.12162936,35.6384002 5.39759301,32.3744252 C2.61846996,29.0444446 1,24.7988538 1,20.2294902 L1,20.2294902 L1,6.84221856 L16.5,1.06715482 Z"></path>
              <path d="M16.5,12.277285 L20.7935249,21.0617434 L26.6777563,17.9345103 L25.1551122,27 L8.01001988,27 L6.35541061,17.9521372 L12.2064751,21.0617434 L16.5,12.277285 Z"></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGModeration
