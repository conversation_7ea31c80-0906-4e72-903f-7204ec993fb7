import { Box, Button, Divider, Stack } from '@mui/material'
import useMediaQuery from '@mui/material/useMediaQuery'

import { IMembership } from '@/shared-types/interfaces'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import CommunityListingItem from '../communities/community-listing-item'

interface CommunityListProps {
  memberships: IMembership[]
}

export default function CommunityList({ memberships }: CommunityListProps) {
  const { isDarkTheme, theme } = useAppTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  return (
    <Box className="rounded-container padded bg-pure-white dark:bg-dark-background-3">
      <Stack
        divider={
          <Divider
            orientation="horizontal"
            flexItem
            sx={{ borderColor: 'var(--divider-border-color)' }}
          />
        }
        spacing={4}
      >
        {memberships.map((membership) => (
          <Box key={membership.id}>
            <Stack direction="row">
              <CommunityListingItem membership={membership} sx={{ flexGrow: 1 }} />
              <Box sx={{ pt: '4px' }}>
                <Button
                  className="demi round-small text-medium padding-17"
                  sx={{
                    color: isDarkTheme ? 'var(--pure-white)' : 'var(--font-light-ui-gray)',
                    backgroundColor: isDarkTheme ? 'var(--black-1100)' : 'var(--ui-dark-300)',
                    pl: { xs: '20px', md: '25px' },
                    pr: { xs: '20px', md: '25px' },
                  }}
                >
                  {isMobile ? 'Join' : 'Ask to join'}
                </Button>
              </Box>
            </Stack>
          </Box>
        ))}
      </Stack>
    </Box>
  )
}
