import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'
import InfoIcon from '@mui/icons-material/Info'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import React, { useState } from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { formatEventDateTime } from '@memberup/shared/src/libs/date-utils'
import { EVENT_LOCATION_TYPE_ENUM, EVENT_VIEW_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IEvent } from '@memberup/shared/src/types/interfaces'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from '@/components/ui/sonner'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { deleteEventApi } from '@/shared-services/apis/event.api'

const badgeSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  p: '2px',
  pl: '6px',
  pr: '8px',
  // width: 68,
  height: 20,
  borderRadius: '10px',
  backgroundColor: 'rgba(88, 93, 102, 0.6)',
  position: 'absolute',
  left: 8,
  top: 8,
}

const EventCard: React.FC<{
  editable?: boolean
  appEvent: IEvent
  eventViewType?: EVENT_VIEW_TYPE_ENUM | 'now'
  onClick?: () => void
  onDeleteSuccess?: () => void
  onEditEventClick?: (event: IEvent) => void
  useLocalTimeZone?: boolean
}> = ({ editable, appEvent, eventViewType, onClick, onDeleteSuccess, onEditEventClick, useLocalTimeZone }) => {
  const { isCurrentUserAdmin } = useCheckUserRole()
  const user = useAppSelector((state) => selectUser(state))
  const [requestDelete, setRequestDelete] = useState(false)
  const [openWarningDelete, setOpenWarningDelete] = useState(false)
  const membership = useAppSelector((state) => selectMembership(state))

  const handleEditEventClick = () => {
    onEditEventClick(appEvent)
  }

  const handleDeleteEventClick = () => {
    setOpenWarningDelete(true)
  }

  const handleDeleteEvent = async () => {
    setRequestDelete(true)
    const res = await deleteEventApi(appEvent.id, membership.id)
    toast.success('Event deleted successfully')
    setOpenWarningDelete(false)
    onDeleteSuccess?.()
    setRequestDelete(false)
  }
  const selectedTimeZone = useLocalTimeZone ? Intl.DateTimeFormat().resolvedOptions().timeZone : appEvent.time_zone

  let badge = null

  if (eventViewType === EVENT_VIEW_TYPE_ENUM.past) {
    badge = (
      <Box sx={badgeSx}>
        <InfoIcon sx={{ color: '#fff', fontSize: '16px' }} />
        <Typography component="span" variant="body2" sx={{ color: '#fff', fontSize: 10, mt: '1px', ml: '4px' }}>
          <b>ENDED</b>
        </Typography>
      </Box>
    )
  } else if (eventViewType === EVENT_VIEW_TYPE_ENUM.draft) {
    badge = (
      <Box sx={badgeSx}>
        <Typography component="span" variant="body2" sx={{ color: '#fff', fontSize: 10, mt: '1px', ml: '4px' }}>
          <b>DRAFT</b>
        </Typography>
      </Box>
    )
  } else if (
    eventViewType === 'now' &&
    (appEvent.location_type === EVENT_LOCATION_TYPE_ENUM.in_person ||
      appEvent.location_type === EVENT_LOCATION_TYPE_ENUM.url)
  ) {
    badge = (
      <Box
        sx={{
          ...badgeSx,
          backgroundColor: '#F34646',
        }}
      >
        <FiberManualRecordIcon sx={{ color: '#FFFFFF', fontSize: '16px' }} />
        <Typography component="span" variant="body2" sx={{ fontSize: 10, mt: '1px', ml: '2px', color: '#FFFFFF' }}>
          <b>LIVE</b>
        </Typography>
      </Box>
    )
  }

  const formattedEventDateTime = formatEventDateTime(appEvent, selectedTimeZone)

  return (
    <div
      className={
        'relative flex cursor-pointer gap-4 rounded-[10px] bg-black-300 p-4 transition-colors hover:bg-black-200'
      }
      data-testid="event-card"
      onClick={onClick}
    >
      {/* Left Column - Event Image */}
      <div className="relative flex-shrink-0">
        <div className="h-[120px] w-[200px] overflow-hidden rounded-lg bg-gray-200">
          {Boolean(appEvent.header_image) ? (
            <AppImg
              src={appEvent.header_image}
              alt="Event Header Image"
              width={200}
              height={120}
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gradient-to-br from-gray-300 to-gray-400">
              <span className="text-sm text-gray-600">No Image</span>
            </div>
          )}
          {badge}
        </div>
      </div>

      {/* Right Column - Event Information */}
      <div className="flex flex-1 flex-col justify-start">
        <div className="mb-2 text-sm text-gray-300">{formattedEventDateTime}</div>

        <h3 className="text-white mb-2 text-lg font-semibold leading-tight">{appEvent.title}</h3>

        <div className="line-clamp-3 text-sm text-gray-300">{appEvent.description}</div>
      </div>

      {/* Dropdown Menu */}
      {editable && (isCurrentUserAdmin || appEvent.created_by === user?.id) && (
        <div className="absolute right-2 top-2">
          <DropdownMenu>
            <DropdownMenuTrigger
              className="bg-black rounded-full bg-opacity-50 p-1 transition-all hover:bg-opacity-70"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizIcon className="text-white" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  handleEditEventClick()
                }}
              >
                Edit Event
              </DropdownMenuItem>
              <DropdownMenuItem
                className="cursor-pointer text-red-500"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDeleteEventClick()
                }}
              >
                Delete Event
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
      <ConfirmModal
        onConfirm={handleDeleteEvent}
        open={openWarningDelete}
        onClose={() => setOpenWarningDelete(false)}
        title={'Delete event'}
        loading={requestDelete}
      >
        <>
          <div>{`Are you sure you want to delete this event?`}</div>
        </>
      </ConfirmModal>
    </div>
  )
}

export default EventCard
