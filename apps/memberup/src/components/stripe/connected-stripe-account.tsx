import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import CreateIcon from '@mui/icons-material/Create'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React from 'react'

import { AppPaymentMethodNumber } from '@memberup/shared/src/components/common/payment-method-number'
import { IStripeAccount } from '@memberup/shared/src/types/interfaces'

const useStyles = makeStyles((theme) => ({
  root: {
    padding: 24,
    width: '100%',
  },
}))

const ConnectedStripeAccount: React.FC<{ connectedStripeAccount: IStripeAccount }> = ({ connectedStripeAccount }) => {
  const classes = useStyles()
  const businessProfile = connectedStripeAccount?.business_profile
  const bankAccount =
    connectedStripeAccount?.external_accounts?.data && connectedStripeAccount.external_accounts.data[0]

  return (
    <div className={classes.root}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item xs>
              <Typography color="text.disabled" variant="body1">
                Connected Account
              </Typography>
            </Grid>
            <Grid item>
              <Button
                className="round-small text-small bold app-button-xxs"
                variant="outlined"
                size="small"
                color="inherit"
                disabled
                startIcon={<CreateIcon fontSize="small" />}
              >
                Edit
              </Button>
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item>
              <Typography variant="h5">{connectedStripeAccount?.business_profile?.name || ''}</Typography>
            </Grid>
            {connectedStripeAccount?.charges_enabled && (
              <Grid item xs>
                <div
                  className="status round-small color06 border-color05"
                  style={{ borderStyle: 'solid', borderWidth: 1 }}
                >
                  Verified
                </div>
              </Grid>
            )}
          </Grid>

          <br />
          <br />
        </Grid>

        <Grid item xs={12} sm={6}>
          <Grid container spacing={2}>
            <Grid item>
              <Typography color="text.disabled" variant="body1" gutterBottom>
                Bank Account
              </Typography>
              <div>
                <AppPaymentMethodNumber number={bankAccount?.last4 || ''} />
              </div>
            </Grid>
            <Grid item>&nbsp;&nbsp;&nbsp;</Grid>
            <Grid item xs>
              <Typography color="text.disabled" variant="body1" gutterBottom>
                Email Address
              </Typography>
              <Typography variant="body1">{connectedStripeAccount?.email || ''}</Typography>
            </Grid>
          </Grid>
          <br />
          <Grid container spacing={2}>
            <Grid item>
              <Typography color="text.disabled" variant="body1" gutterBottom>
                Address
              </Typography>
              <Typography variant="body1">
                {businessProfile?.support_address?.line1},&nbsp;
                {businessProfile?.support_address?.city},&nbsp;
                {businessProfile?.support_address?.state},&nbsp;
                {businessProfile?.support_address?.postal_code}
              </Typography>
            </Grid>
            <Grid item>&nbsp;&nbsp;&nbsp;</Grid>
            <Grid item xs>
              <Typography color="text.disabled" variant="body1" gutterBottom>
                Phone
              </Typography>
              <Typography variant="body1">{businessProfile?.support_phone || 'No phone number'}</Typography>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div
            className="background-color03"
            style={{ borderRadius: 12, padding: '28px 0', maxWidth: 430, marginLeft: 'auto' }}
          >
            <Grid container>
              <Grid className="d-flex align-center justify-center" item xs>
                <div>
                  <Typography variant="body1" gutterBottom>
                    Payments
                  </Typography>
                  <div className="d-flex align-center">
                    <CheckCircleIcon color={connectedStripeAccount?.charges_enabled ? 'primary' : 'disabled'} />
                    <span>
                      &nbsp;
                      {connectedStripeAccount?.charges_enabled ? 'Enabled' : 'Not Enabled'}
                    </span>
                  </div>
                </div>
              </Grid>
              <Grid item>
                <div className="background-color03" style={{ width: 2, height: 48 }}></div>
              </Grid>
              <Grid className="d-flex align-center justify-center" item xs>
                <div>
                  <Typography variant="body1" gutterBottom>
                    Payouts
                  </Typography>
                  <div className="d-flex align-center">
                    <CheckCircleIcon color={connectedStripeAccount?.payouts_enabled ? 'primary' : 'disabled'} />
                    <span>
                      &nbsp;
                      {connectedStripeAccount?.payouts_enabled ? 'Enabled' : 'Not Enabled'}
                    </span>
                  </div>
                </div>
              </Grid>
            </Grid>
          </div>
        </Grid>
      </Grid>
    </div>
  )
}

export default ConnectedStripeAccount
