import UpgradePlan from '@/memberup/components/dialogs/settings/upgrade-plan'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { MEMBERUP_PLANS } from '@memberup/shared/src/settings/plans'
import { MEMBERUP_PLAN_ENUM, RECURRING_INTERVAL_ENUM } from '@memberup/shared/src/types/enum'
import CheckIcon from '@mui/icons-material/Check'
import CheckBoxIcon from '@mui/icons-material/CheckBox'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import Accordion from '@mui/material/Accordion'
import AccordionDetails from '@mui/material/AccordionDetails'
import AccordionSummary from '@mui/material/AccordionSummary'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Grid from '@mui/material/Grid'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import Typography from '@mui/material/Typography'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import { useRouter } from 'next/router'
import React, { useState } from 'react'
import { formatDate } from '@/shared-libs/date-utils'

const usages = [
  ['Members', '1,000', '10,000', '100,000'],
  ['Spaces', '10', '20', 'Unlimited'],
  ['Content Storage', '10GB', '100GB', '1TB'],
  ['Transaction fees', '4%', '1%', '0%'],
]

const features = [
  ['Spark (DFY Engagement)', '1', '1', '1'],
  ['Custom Domain', '1', '1', '1'],
  ['Interactive Spaces', '1', '1', '1'],
  ['Unlimited Admins', '1', '1', '1'],
  ['Unlimited Events', '1', '1', '1'],
  ['In App Notifications', '1', '1', '1'],
  ['Community Wide Search', '1', '1', '1'],
  ['Member Profiles & Directory', '1', '1', '1'],
  ['Direct + Group Messaging', '1', '1', '1'],
  ['Community Moderation', '1', '1', '1'],
  ['Course Hosting', '1', '1', '1'],
  ['Data Ownership', '1', '1', '1'],
  ['Bulk Import + Export', '1', '1', '1'],
  ['Subscriptions + One-Time Payments', '1', '1', '1'],
  ['MemberUp University', '1', '1', '1'],
]

const supports = [
  ['Email Support', '1', '1', '1'],
  ['Priority Chat Support', '0', '0', '1'],
]

const questions = [
  {
    title: `Do you have a satisfaction guarantee?`,
    content: `We have a 100% satisfaction guarantee. If for any reason, you're not satisfied with your purchase, simply let us know within 14 days and we'll be happy to issue you a full refund.`,
  },
  {
    title: `Do I lose data if I cancel?`,
    content: `Nope, we'll keep it safe! As with most "software-as-a-service" platforms, when you cancel your account, your data will become inaccessible, but don't worry, before canceling you have the opportunity to download CSV files of your contacts and members. You also have the opportunity to reactivate and reclaim your account within 30 days.`,
  },
  {
    title: `Support for beginners?`,
    content: `We support entrepreneurs of all levels within MemberUp University! MemberUp University is a community where you can network with and connect to other community leaders, ask questions, attend live events, have your community reviewed, see our product roadmap, request features and learn what’s working in other communities. You'll also receive access to a full course on community building from industry leaders (worth $1,999).`,
  },
  {
    title: `Can you handle the load?`,
    content: `Yes! We're built to scale with your business (unlike WordPress) 🤭 Because MemberUp is hosted on the largest public cloud cluster in the world (powered by Amazon and backed up by Global Edge Network + CDN) we have virtually unlimited ability to scale in real time. Whether you send 100 visitors or 100,000+ today, it won't slow us down!`,
  },
  {
    title: `Who owns the data?`,
    content: `Your data is your business! Any content (including your users) is 100% owned by you. You have access to your members emails, you own the relationship, and we don't get in the way.`,
  },
  {
    title: `Is MemberUp Secure?`,
    content: `Yes! Security is our top priority. We built MemberUp from the ground up to make sure your data and members area are secure. You don't have to worry about staying up-to-date with "plugins" that can easily be compromised. We are SOC2 Type 2 Compliant, GDPR Compliant, and do not store any credit card information on behalf of you or your members.`,
  },
]

const useStyles = makeStyles((theme) => ({
  root: {
    width: '100%',
    maxWidth: '100%',
    minWidth: 380,
    marginLeft: 'auto',
    '& .MuiSelect-select': {
      paddingLeft: 8,
      paddingTop: 8,
      paddingBottom: 8,
    },
    '& .MuiInputBase-root': {
      width: '100%',
      paddingLeft: 8,
    },
    '& .Mui-expanded': {
      marginTop: 4,
      marginBottom: 4,
    },
  },
  subSection: {
    borderColor: theme.palette.divider,
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 1,
    padding: 32,
    position: 'relative',
  },
  activeSubSection: {
    borderWidth: 2,
  },
  radio: {
    position: 'absolute',
    left: 4,
    top: 4,
  },
  planButtonsWrapper: {
    borderColor: theme.palette.divider,
    borderStyle: 'solid',
    borderWidth: 3,
    borderRadius: 28,
    padding: 8,
    width: '100%',
    maxWidth: 510,
    margin: 'auto',
  },
  planButton: {
    borderRadius: 20,
    cursor: 'pointer',
    padding: 8,
    '&.active': {
      backgroundColor: '#a5ca85',
      color: theme.palette.background.default,
    },
  },
  gridItem: {
    borderColor: theme.palette.divider,
    borderStyle: 'solid',
    borderWidth: 1,
    height: '100%',
    padding: 24,
  },
  plan: {
    height: '100%',
    position: 'relative',
  },
  accordion: {
    borderRadius: 8,
    marginTop: 4,
    marginBottom: 4,
  },
  accordionSummary: {
    padding: 16,
  },
  accordionDetails: {
    padding: 16,
    paddingTop: 0,
  },
}))

const PlanItem: React.FC<{
  plan
  isAnnual: boolean
  isEnabledAnnual: boolean
  recurringInterval: RECURRING_INTERVAL_ENUM
  currentPlan?: MEMBERUP_PLAN_ENUM
  onClick: () => void
  membershipSetting: any
}> = ({
  membershipSetting,
  plan,
  isAnnual,
  isEnabledAnnual,
  recurringInterval,
  currentPlan,
  onClick,
}) => {
  const classes = useStyles()
  const monthlyNormalPrice = isAnnual ? plan.annualNormalMonthlyPrice : plan.monthlyNormalPrice
  const savePrice = isAnnual ? plan.annualSave : plan.monthlySave
  const isCurrentPlan = plan.name === currentPlan && isAnnual === isEnabledAnnual
  const isDowngradePlan =
    !isCurrentPlan &&
    currentPlan &&
    (currentPlan === MEMBERUP_PLAN_ENUM.enterprise ||
      (currentPlan === MEMBERUP_PLAN_ENUM.pro && plan.name === MEMBERUP_PLAN_ENUM.basic))
  const buttonText = isDowngradePlan
    ? 'CONTACT US'
    : isCurrentPlan
      ? plan.allowPayEarly
        ? 'PAY EARLY'
        : 'YOUR CURRENT PLAN'
      : currentPlan
        ? 'UPGRADE'
        : 'ACTIVATE'

  const today = new Date()
  const endDate = new Date(membershipSetting.stripe_subscription_period_end_at * 1000)
  // Calculate the difference in milliseconds
  // @ts-ignore
  const differenceInMilliseconds = endDate - today
  // Convert milliseconds to days
  const daysToRenew = Math.ceil(differenceInMilliseconds / (1000 * 60 * 60 * 24))
  const extendsToMoreThanOneYear = daysToRenew >= 300
  const isDisabled =
    (isCurrentPlan && (!plan.allowPayEarly || extendsToMoreThanOneYear)) || isDowngradePlan

  return (
    <Box className={classes.plan}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Typography variant="subtitle1">
            <b>{plan.displayName}</b>
          </Typography>
        </Grid>
        <Grid item xs={12}>
          {Boolean(monthlyNormalPrice) && (
            <Typography variant="body1" sx={{ mb: 2, textDecoration: 'line-through' }}>
              Normally ${monthlyNormalPrice}
            </Typography>
          )}
          <Typography className="bold" variant="h6">
            <>
              {' '}
              <span style={{ fontSize: 32 }}>
                $
                {plan.promotional ? (
                  plan.annualPrice
                ) : isAnnual ? (
                  <>
                    {plan.annualMonthlyPrice}/<span>mo</span>
                  </>
                ) : (
                  <>
                    {plan.monthlyPrice}/<span>mo</span>
                  </>
                )}
              </span>
            </>
          </Typography>
          {Boolean(savePrice) && (
            <Typography variant="body1" sx={{ mt: 2 }}>
              {plan.name === MEMBERUP_PLAN_ENUM.enterprise ? (
                <span>Discounted Forever (Save $2591)</span>
              ) : (
                <>
                  {isAnnual && <span>Billed annually&nbsp;</span>}
                  <b>
                    (Save ${savePrice}/{isAnnual ? 'yr' : 'mo'})
                  </b>
                </>
              )}
            </Typography>
          )}
        </Grid>
        <Grid item xs={12}>
          <Button
            className="round-small"
            variant="contained"
            disabled={isDisabled}
            fullWidth
            sx={{
              backgroundColor: `${
                isDisabled
                  ? '#cccccc'
                  : plan.backgroundColor === 'transparent'
                    ? '#cccccc'
                    : plan.backgroundColor || '#cccccc'
              }!important`,
              color: isDisabled ? '#808080!important' : '#000000!important',
              '&:hover': isDisabled
                ? undefined
                : {
                    backgroundColor: `${plan.backgroundHColor}!important`,
                  },
            }}
            onClick={onClick}
          >
            {buttonText}
          </Button>
        </Grid>
        <Grid item xs={12}>
          <Typography
            variant="body1"
            sx={{
              height: plan.name === 'basic' ? 35 : 52,
              fontFamily: 'Graphik Medium',
            }}
          >
            {plan.description}
          </Typography>
        </Grid>
        <Grid item xs={12}>
          {Boolean(plan.options) && (
            <Grid container spacing={2}>
              {plan.options.map((option, i) => (
                <Grid key={`option-${i}`} item xs={12} sx={{ paddingTop: '12px !important' }}>
                  <Grid container alignItems="center" spacing={2} sx={{ lineHeight: '16.8px' }}>
                    {i === 1 && (plan.name === 'pro' || plan.name === 'enterprise') ? (
                      <Grid className="text-ellipsis" item xs>
                        <Typography
                          variant="h6"
                          sx={{
                            fontFamily: 'Graphik SemiBold',
                            fontSize: '14px',
                            padding: '10px 0',
                          }}
                        >
                          <Box sx={{ paddingTop: '2px !important' }}>{option}</Box>
                        </Typography>
                      </Grid>
                    ) : (
                      <>
                        <Grid item>
                          <CheckBoxIcon sx={{ color: '#7b51e0' }} />
                        </Grid>
                        <Grid className="text-ellipsis" item xs sx={{ fontSize: '14px' }}>
                          <Box sx={{ paddingTop: '2px !important' }}>{option}</Box>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </Grid>
              ))}
            </Grid>
          )}
        </Grid>
      </Grid>
    </Box>
  )
}

const MemberupPlan = () => {
  const classes = useStyles()
  const theme = useTheme()
  const router = useRouter()
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const [recurringInterval, setRecurringInterval] = useState(RECURRING_INTERVAL_ENUM.year)
  const [openUpgrade, setOpenUpgrade] = useState({
    open: false,
    plan: null,
  })

  const currentPlan =
    membershipSetting?.plan && MEMBERUP_PLANS.find((p) => p.name === membershipSetting.plan)
  const isEnabledAnnual = membershipSetting?.stripe_enable_annual || false
  const isAnnual = recurringInterval === RECURRING_INTERVAL_ENUM.year

  const subscriptionRenewDate = new Date(
    membershipSetting?.stripe_subscription_period_end_at * 1000
  )
  const subscriptionRenewDateStr = formatDate({
    date: subscriptionRenewDate,
    format: 'MMMM d, yyyy',
  })
  const handleUpgrade = (plan) => {
    if (membershipSetting?.plan) {
      setOpenUpgrade({ open: true, plan })
    } else {
      router.push(
        {
          pathname: `/plan/upgrade/${plan.name}`,
          query: { recurring_interval: recurringInterval },
        },
        undefined,
        { shallow: false }
      )
    }
  }

  const memberupPlans = MEMBERUP_PLANS.filter((p) => p.enabled)

  return (
    <Box sx={{ paddingTop: 4, paddingBottom: 4, margin: 'auto', maxWidth: 1280 }}>
      <Box className="text-center" sx={{ mb: 4, '& img': { display: 'inline-block' } }}>
        <AppImg
          src={`/assets/default/logos/memberup-logo.png`}
          width={190}
          height={30}
          alt="MemberUp Logo"
        />
      </Box>
      <Typography className="text-center" variant="h2" sx={{ mb: 2 }}>
        Upgrade Your Plan
      </Typography>
      {/* <Box className="text-center" sx={{ mb: '48px' }}>
        <Typography className="text-center" variant="h4">
          <AppImg
            src={`/assets/default/images/bend-arrow_right.png`}
            width={85}
            height={85}
            style={{ marginBottom: '-25px' }}
            alt="Arrow"
          />
          Plus we&rsquo;re giving away a $100,000 MemberUp Experience.
        </Typography>
      </Box> */}
      {/* <Box className={classes.planButtonsWrapper}>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <div
              className={clsx(classes.planButton, {
                active: isAnnual,
              })}
              onClick={() => setRecurringInterval(RECURRING_INTERVAL_ENUM.year)}
            >
              <Typography className="text-center" color="inherit" variant="h6">
                Annual Discount
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6}>
            <div
              className={clsx(classes.planButton, {
                active: !isAnnual,
              })}
              onClick={() => setRecurringInterval(RECURRING_INTERVAL_ENUM.month)}
            >
              <Typography align="center" color="inherit" variant="h6">
                Monthly Plans
              </Typography>
            </div>
          </Grid>
        </Grid>
      </Box> */}
      <Box sx={{ minHeight: 30, mt: '28px' }}>
        {Boolean(currentPlan) && (
          <Typography variant="h6" align="center" sx={{ lineHeight: 1.9, mb: '42px' }}>
            Your current plan is&nbsp;{currentPlan.displayName}, billed&nbsp;
            {isEnabledAnnual ? 'annually' : 'monthly'} and renews on {subscriptionRenewDateStr}
          </Typography>
        )}
      </Box>
      {isAnnual ? (
        <>
          <Grid container spacing={4}>
            {memberupPlans.map((item, index) => (
              <Grid key={`plan-${index}`} item xs={6} sm={4}>
                <Box
                  sx={{
                    backgroundColor: item.backgroundColor || 'transparent',
                    borderRadius: '20px',
                    borderBottomLeftRadius: '22px',
                    borderBottomRightRadius: '22px',
                    boxShadow: item.containerBoxShadow,
                  }}
                >
                  <Typography
                    className="bold"
                    align="center"
                    variant="h6"
                    sx={{ p: 3, position: 'relative', color: '#000000', height: 48 }}
                  >
                    {Boolean(item.labelIcon) && (
                      <span
                        style={{
                          position: 'absolute',
                          fontSize: 47,
                          display: 'block',
                          left: 24,
                          top: -16,
                          transform: 'scale(1, 1) translate(0px, 0px) rotate(347deg)',
                        }}
                      >
                        😍
                      </span>
                    )}
                    {index > 0 && <span>{item.label}</span>}
                  </Typography>
                  <Box
                    sx={{
                      backgroundColor: theme.palette.background.paper,
                      borderColor: theme.palette.divider,
                      borderRadius: '20px',
                      borderStyle: 'solid',
                      borderWidth: 1,
                      boxShadow: item.boxShadow,
                      padding: '24px',
                    }}
                  >
                    <PlanItem
                      plan={item}
                      isAnnual={isAnnual}
                      isEnabledAnnual={isEnabledAnnual}
                      recurringInterval={recurringInterval}
                      currentPlan={membershipSetting?.plan}
                      onClick={() => handleUpgrade(item)}
                      membershipSetting={membershipSetting}
                    />
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
          <Typography
            className="text-center"
            variant="h4"
            gutterBottom
            sx={{ fontSize: 24, mb: '48px', mt: '70px' }}
          >
            Easily compare plans & features
            <AppImg
              src={`/assets/default/images/bend-arrow_down.png`}
              width={62}
              height={56}
              style={{ marginBottom: '-42px' }}
              alt="Arrow"
            />
          </Typography>
          {/* <Typography variant="h5">Community features</Typography> */}
          {/* <Typography variant="h5">MemberUp core features</Typography> */}
          {/* <Typography variant="h5">Add-ons</Typography> */}
          <TableContainer
            sx={{
              borderRadius: 3,
              borderWidth: 1,
              borderColor: theme.palette.divider,
              borderStyle: 'solid',
              p: 4,
              pt: 2,
              '& .MuiTableCell-head': {
                borderBottom: 'none',
                fontSize: 20,
                fontWeight: 'bold',
              },
              '& .MuiTableCell-root': {
                paddingTop: '36px',
              },
              '& .MuiTableRow-root': {
                '&>.MuiTableCell-root:nth-of-type(3)': {
                  color: '#a5ca85',
                },
                '&>.MuiTableCell-root:nth-of-type(4)': {
                  color: '#7b51e0',
                },
              },
            }}
          >
            <Table stickyHeader aria-label="sticky table">
              <TableHead>
                <TableRow>
                  <TableCell></TableCell>
                  <TableCell style={{ width: '20%' }}>Inspired</TableCell>
                  <TableCell style={{ width: '20%' }}>In-Motion</TableCell>
                  <TableCell style={{ width: '20%' }}>Infinite</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={4} style={{ borderBottom: 'none', paddingTop: 8 }}>
                    <Typography color="text.disabled" variant="subtitle1">
                      <b>USAGE</b>
                    </Typography>
                  </TableCell>
                </TableRow>
                {usages.map((row, index) => (
                  <TableRow key={`row-${index}`} hover>
                    {row.map((item, index) => (
                      <TableCell key={`item-${index}`}>
                        <b>{item}</b>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={4} sx={{ borderBottom: 'none' }}>
                    <Typography color="text.disabled" variant="subtitle1" sx={{ mt: '24px' }}>
                      <b>FEATURES</b>
                    </Typography>
                  </TableCell>
                </TableRow>
                {features.map((row, index) => (
                  <TableRow key={`row-${index}`} hover>
                    {row.map((item, index) => (
                      <TableCell key={`item-${index}`}>
                        {item === '1' ? <CheckIcon /> : item === '0' ? '' : <b>{item}</b>}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
                <TableRow>
                  <TableCell colSpan={4} sx={{ borderBottom: 'none' }}>
                    <Typography color="text.disabled" variant="subtitle1" sx={{ mt: '24px' }}>
                      <b>SUPPORT</b>
                    </Typography>
                  </TableCell>
                </TableRow>
                {supports.map((row, index) => (
                  <TableRow key={`row-${index}`} hover>
                    {row.map((item, index) => (
                      <TableCell key={`item-${index}`}>
                        {item === '1' ? <CheckIcon /> : item === '0' ? '' : <b>{item}</b>}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Typography className="text-center" variant="h5" sx={{ mt: '70px' }}>
            Frequently Asked Questions
          </Typography>
          <br />
          {questions.map((item, i) => (
            <Accordion className={clsx(classes.accordion, 'background-color08')} key={`item-${i}`}>
              <AccordionSummary
                className={classes.accordionSummary}
                expandIcon={<ExpandMoreIcon />}
                aria-controls="question-content"
              >
                <Typography variant="h6">{item.title}</Typography>
              </AccordionSummary>
              <AccordionDetails className={classes.accordionDetails}>
                <Typography variant="body1">{item.content}</Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </>
      ) : (
        <Typography color="text.disabled" variant="body1" align="center">
          Monthly Plan is not available for now.
        </Typography>
      )}
      {openUpgrade.open && (
        <UpgradePlan
          open={openUpgrade.open}
          selectedPlan={openUpgrade.plan}
          recurringInterval={recurringInterval}
          onClose={() => setOpenUpgrade({ open: false, plan: null })}
        />
      )}
    </Box>
  )
}

MemberupPlan.displayName = 'MemberupPlan'
export default MemberupPlan
