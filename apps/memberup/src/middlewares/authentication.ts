import { authOptions } from '@/memberup/pages/api/auth/[...nextauth]'
import { getServerSession } from 'next-auth/next'

// async function getServerSession(req, es, options) {
//   return null
// }

const authenticationMiddleware = async (req, res, next) => {
  try {
    const session = await getServerSession(req, res, authOptions)
    if (!session?.user?.email) return res.status(401).end('Failed Authentication.')
    req['user'] = session.user
    return next()
  } catch (err: any) {
    console.log('err', err)
    if (typeof err === 'string') {
      return res.status(401).end(err)
    } else if (err.message === 'jwt expired') {
      return res.status(401).end('Token Expired. Please login again.')
    } else {
      return res.status(401).end('Invalid Token. Please login again.')
    }
  }
}

export default authenticationMiddleware
