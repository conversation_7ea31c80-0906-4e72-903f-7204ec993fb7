import { knockEvent } from '@/memberup/libs/knock'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createEvent, findEvents } from '@memberup/shared/src/libs/prisma/event'
import {
  EVENT_LOCATION_TYPE_ENUM,
  EVENT_STATUS_ENUM,
  USER_ROLE_ENUM,
} from '@memberup/shared/src/types/enum'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const newWhere = { membership_id: user.current_membership_id, ...(where || {}) }

      if (
        ![USER_ROLE_ENUM.admin, USER_ROLE_ENUM.creator, USER_ROLE_ENUM.owner].includes(
          user.role || (USER_ROLE_ENUM.member as any)
        )
      ) {
        newWhere['status'] = { not: EVENT_STATUS_ENUM.drafts }
      }

      const result = await findEvents({
        where: newWhere,
        take,
        select,
        skip,
        orderBy,
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Event'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const result = await createEvent({
        data: {
          ...req.body,
          attendees: {
            create: [
              {
                user: {
                  connect: {
                    id: user.id,
                  },
                },
              },
            ],
          },
          membership: {
            connect: {
              id: user.current_membership_id,
            },
          },
          creator: {
            connect: {
              id: user.id,
            },
          },
        },
      })

      const currentTime = Math.floor(Date.now() / 1000)
      const hasEventPassed = result.start_time < currentTime

      // Handle notifications
      if (
        result?.notify_members &&
        result.status === EVENT_STATUS_ENUM.published &&
        !hasEventPassed
      ) {
        const cancelationKey = `${result.id}_${result.updatedAt.toISOString()}`
        knockEvent({
          id: result.id,
          location:
            result.location_type === EVENT_LOCATION_TYPE_ENUM.tbd
              ? 'TBD'
              : result.location_type === EVENT_LOCATION_TYPE_ENUM.content_release
                ? 'Content Release'
                : result.location_address,
          start_time: result.start_time,
          title: result.title,
          time_zone: result.time_zone,
          membership_id: user.current_membership_id,
          user_id: user.id,
          cancelationKey: cancelationKey,
          updated: false,
        })
      }

      if (result?.id) {
        return res.status(201).send({ success: true, data: result })
      }
      res.status(500).json(errorHandler(result, 'Event'))
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(500).json(errorHandler(err, 'Event'))
    }
  })

export default handler
