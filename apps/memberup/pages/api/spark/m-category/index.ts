import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import {
  createSparkCategory,
  findSparkCategories,
  findSparkCategoryById,
} from '@/shared-libs/prisma/spark-category'
import {
  createSparkMembershipCategory,
  findSparkMembershipCategory,
  updateSparkMembershipCategory,
} from '@/shared-libs/prisma/spark-m-category'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const tempWhere = {
        AND: [
          {
            OR: [
              {
                membership_id: user.current_membership_id,
              },
              { membership_id: null },
            ],
          },
          { active: true },
        ],
      }

      Object.keys(where || {}).forEach((k) => {
        tempWhere.AND.push({ [k]: where[k] } as any)
      })

      const result = await findSparkCategories({
        where: tempWhere,
        take,
        select,
        skip,
        include: {
          spark_m_categories: {
            where: {
              membership_id: user.current_membership_id,
            },
          },
        },
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'SparkMembershipCategory'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { category_id, name, slug, icon, description, active, started } = req.body

      if (!name) {
        return res.status(400).json({ message: 'You should provide the name of category.' })
      }

      let category
      let result

      if (category_id) {
        category = await findSparkCategoryById({
          where: {
            id: category_id as string,
          },
        })
      }

      if (category?.id) {
        result = await findSparkMembershipCategory({ where: { category_id: category.id } })

        if (result?.id) {
          result = await updateSparkMembershipCategory({
            where: { id: result.id },
            data: {
              active,
              started,
            },
          })
        } else {
          result = await createSparkMembershipCategory(
            {
              data: {
                active,
                started,
                category: {
                  connect: {
                    id: category.id,
                  },
                },
                membership_id: user.current_membership_id as string,
              },
            },
            user.id
          )
        }
      } else {
        result = await createSparkCategory({
          data: {
            active,
            name,
            slug,
            icon: icon || '',
            description: description || '',
            membership_id: user.current_membership_id as string,
          },
        })
      }
      return res.status(201).send({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkMembershipCategory'))
    }
  })

export default handler
