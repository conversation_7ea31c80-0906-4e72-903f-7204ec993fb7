import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { knockTriggerWorkflow } from '@/shared-libs/knock'
import { boostedPrisma } from '@/shared-libs/prisma/prisma'
import { getFullName } from '@/shared-libs/profile'
import { stripHtml } from '@/shared-libs/string-utils'
import { KNOCK_WORKFLOW_ENUM } from '@/shared-types/enum'
import { findFeedById } from '@memberup/shared/src/libs/prisma/feed'
import prisma from '@memberup/shared/src/libs/prisma/prisma'
import { handleUserAction } from '@memberup/shared/src/libs/prisma/reaction'
import { createStreamLike, deleteStreamReaction } from '@memberup/shared/src/libs/stream-chat'
import { CHANNEL_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .put(async (req, res) => {
    const { id } = req.query
    const user = req['user']

    // TODO MEM-1928: Check what to return on 404 errors
    const feed = await boostedPrisma.feed.findUnique({
      where: {
        id: id as string,
      },
      include: {
        user: {
          include: {
            membership: true,
          },
        },
      },
    })

    if (!feed) {
      return res.status(404).end()
    }

    const isComment = feed.hierarchy_order ? true : false

    // try to upsert a reaction
    const type = 'like'
    const message_id = feed.id
    const user_id = user.id
    const metadata = {}

    const reaction = await prisma.reaction.upsert({
      where: { type_message_user_unique: { type, message_id, user_id } },
      create: {
        type,
        message_id,
        user_id,
        metadata,
      },
      update: {
        metadata,
      },
    })

    const wasCreated = reaction.createdAt.getTime() === reaction.updatedAt.getTime()

    if (wasCreated) {
      const reactionData = {
        id: reaction.id,
        type: 'like',
        user_id: user.id,
      }
      await createStreamLike(CHANNEL_TYPE_ENUM.team, feed.channel_id, feed.id, reactionData)
      await handleUserAction(user, 'like', feed.user_id, 'LIKE', feed.id)

      if (feed.user.id !== user.id) {
        let notificationURL = ''
        if (isComment) {
          const parentMessage = await boostedPrisma.feed.findUnique({
            where: {
              id: feed.parent_id as string,
            },
            include: {
              user: {
                include: {
                  membership: true,
                },
              },
            },
          })

          if (parentMessage.permalink) {
            notificationURL = `/post/${parentMessage.permalink}`
          } else {
            notificationURL = `/post/${parentMessage.id}`
          }
        } else {
          if (feed.permalink) {
            notificationURL = `/post/${feed.permalink}`
          } else {
            notificationURL = `/post/${feed.id}`
          }
        }

        const messageType = isComment ? 'comment' : 'post'
        let messageContent = feed.title

        if (messageType === 'comment') {
          messageContent = stripHtml(feed.text)
        }

        knockTriggerWorkflow(
          user.membership_id,
          KNOCK_WORKFLOW_ENUM.new_like,
          user.id,
          [feed.user.id],
          {
            id: reaction.id,
            actor_name: getFullName(user.first_name, user.last_name),
            message_type: messageType,
            message_content: messageContent,
            community_name: feed.user.membership.name,
            url: notificationURL,
          }
        )
      }
    }

    return res.status(200).json(reaction)
  })
  .delete(async (req, res) => {
    const { id } = req.query
    const user = req['user']
    const feed = await findFeedById(id as string)

    if (!feed) {
      return res.status(404).end()
    }

    const type = 'like'
    const result = await prisma.reaction.deleteMany({
      where: {
        user_id: user.id,
        message_id: feed.id,
        type: type,
      },
    })

    if (result.count > 0) {
      await deleteStreamReaction(feed.id, 'like', user.id)
      await handleUserAction(user, 'unlike', feed.user_id, 'LIKE', feed.id)
    }

    return res.status(200).json({ success: true })
  })

export default handler
