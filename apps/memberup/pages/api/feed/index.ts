import { mentionGetMemberIdsIfEverbodyMentioned } from '@/memberup/libs/mentions'
import { getMuxAssets } from '@/memberup/libs/mux'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { createActionHistory } from '@/shared-libs/prisma/actions'
import {
  createFeed,
  findFeedById,
  findFeedByPermalink,
  getFeaturedCommenters,
} from '@/shared-libs/prisma/feed'
import { findMembershipById } from '@/shared-libs/prisma/membership'
import prisma from '@/shared-libs/prisma/prisma'
import { getFullName } from '@/shared-libs/profile'
import { stripHtml } from '@/shared-libs/string-utils'
import { ACTION_NAME_ENUM, USER_ROLE_ENUM, USER_STATUS_ENUM } from '@/shared-types/enum'
import { IMembership } from '@/shared-types/interfaces'
import { knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { findChannel } from '@memberup/shared/src/libs/prisma/channel'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { updateFeed } from '@memberup/shared/src/libs/prisma/feed'
import { updateStreamMessage } from '@memberup/shared/src/libs/stream-chat'
import processLinks from '@memberup/shared/src/services/functions/link_parser'
import { FEED_TYPE_ENUM, KNOCK_WORKFLOW_ENUM } from '@memberup/shared/src/types/enum'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

export const getPostByPermalink = async (req, res) => {
  const user = req['user']
  try {
    let result = null
    if (!req.query.permalink) {
      return res.status(400).json({
        message: `You must specify a permalink.`,
      })
    } else {
      const urlDecodedPermalink: string = decodeURIComponent(req.query.permalink as string)
      result = await findFeedByPermalink(urlDecodedPermalink, user.current_membership_id)
      if (!result) {
        return res.status(404).json({
          message: `Post not found.`,
        })
      }
    }
    res.json({ success: true, data: result })
  } catch (err: any) {
    console.log('getFeedsError =====', err)
    sentryCaptureException(err)
    res.status(500).json(errorHandler(err, 'Post'))
  }
}

handler
  .use(authenticationMiddleware)
  .get(getPostByPermalink)
  .post(async (req, res) => {
    try {
      const user = req['user']

      const {
        html,
        title,
        text,
        channel_id,
        feed_type,
        parent_id,
        attachments,
        mentioned_users,
        parent_permalink,
        reply_parent_id,
        host,
        membership_id,
      } = req.body

      // Validate parent_id
      if (feed_type === FEED_TYPE_ENUM.comment && !parent_id) {
        return res.status(400).json({
          message: `You must specify the parent_id for comments and replies.`,
        })
      }

      // Validate reply_parent_id
      if (feed_type === FEED_TYPE_ENUM.comment && !reply_parent_id) {
        return res.status(400).json({
          message: `You must specify the reply_parent_id for comments and replies.`,
        })
      }

      let membershipId = membership_id

      let membership: IMembership = await findMembershipById({
        where: { id: membershipId },
      })

      const data = {
        feed_type,
        html,
        title,
        text,
        channel: {
          connect: {
            id: channel_id,
          },
        },
        user: {
          connect: {
            id: user.id,
          },
        },
      }

      if (feed_type === FEED_TYPE_ENUM.default) {
        const channel = await findChannel(channel_id, user.current_membership_id)
        if (!channel?.id) {
          return res.status(400).json({ message: 'Invalid space provided.' })
        }

        // For members, do not allow to create posts on private or the home (Community) channel
        if (
          (channel.is_private || channel.slug === 'home') &&
          user.role === USER_ROLE_ENUM.member
        ) {
          return res.status(403).json({ message: 'You are not allowed to post on private spaces.' })
        }

        data['last_activity_at'] = new Date().toISOString()
        if (attachments?.length) {
          data['attachments'] = []
          for (const attachment of attachments) {
            const { uploaded_date, ...rest } = attachment
            if (rest.mux_upload_id) {
              const assets = await getMuxAssets({ upload_id: rest.mux_upload_id })
              if (assets.length) {
                const {
                  id,
                  status,
                  // duration,
                  aspect_ratio,
                  playback_ids,
                  passthrough,
                } = assets[0]

                rest.mux_asset = {
                  id,
                  status,
                  // duration,
                  aspect_ratio,
                  playback_ids,
                  passthrough,
                  tracks: [],
                }
              }
            }
            data['attachments'].push(rest)
          }
        }
      } else if (feed_type === FEED_TYPE_ENUM.comment) {
        const parentPost = await findFeedById(parent_id)
        if (!parentPost) {
          return res.status(400).json({
            message: `Parent post not found.`,
          })
        }

        data['parent_id'] = parent_id
        data['reply_parent_id'] = reply_parent_id
      }

      data['metadata'] = {}
      if (mentioned_users?.length) {
        data['metadata'] = {
          mentioned_users: mentioned_users.filter((user) => user.id !== '-1'),
        }
      }

      const externalLinksMetadata = await processLinks(text)
      data['metadata'].links = externalLinksMetadata.links ?? undefined

      const mentionedUsersIds = mentioned_users?.map((user) => user.id).filter((id) => id !== '-1')
      const result = await createFeed({
        data,
      })

      // Handle parent post update in case of a comment
      if (feed_type === FEED_TYPE_ENUM.comment) {
        // Update database post
        const lastActivityAt = new Date().toISOString()
        await updateFeed({
          where: { id: parent_id },
          data: {
            last_activity_at: lastActivityAt,
          },
        })
        // Update GetStream post
        const upsertParentData = {
          featured_commenters: await getFeaturedCommenters(parent_id),
          last_activity_at: lastActivityAt,
          // TODO: Use ISO DateTime instead
          latest_comment_timestamp: Math.floor(Date.now() / 1000),
        }
        await updateStreamMessage(parent_id, user.id, upsertParentData)
      }

      const permalink = `/post/${result.permalink}`

      if (result?.id) {
        const action = await prisma.action.findFirst({
          where: {
            action_name:
              feed_type === FEED_TYPE_ENUM.comment
                ? ACTION_NAME_ENUM.COMMENT_CREATED
                : ACTION_NAME_ENUM.POST_CREATED,
          },
        })

        await createActionHistory(user.id, action, result.id)
        const notifyEveryone = mentionGetMemberIdsIfEverbodyMentioned(mentioned_users, user)
        const messageType = result.hierarchy_order ? 'comment' : 'post'

        let messageContent = result.title
        let parentPermalink = null
        let actorName = getFullName(user.first_name, user.last_name)
        if (messageType === 'comment') {
          parentPermalink = parent_permalink + '?comment_id=' + result.id
          messageContent = stripHtml(result.text)
        }

        const newMentionNotificationPayload = {
          actor_name: actorName,
          message_type: messageType,
          message_content: messageContent,
          url: parentPermalink ?? permalink,
          community_name: membership.name,
          community_slug: `https://${membership.host}`,
          email_post_url: host + (parentPermalink ?? permalink),
        }

        if (notifyEveryone) {
          let recipients = await prisma.user.findMany({
            where: {
              membership_id: user.membership_id,
              status: USER_STATUS_ENUM.active,
              id: {
                not: user.id,
              },
            },
            select: {
              id: true,
            },
          })

          await knockTriggerWorkflow(
            user.membership_id,
            KNOCK_WORKFLOW_ENUM.new_everyone_mention_notification,
            user.id,
            recipients.map((r) => r.id),
            newMentionNotificationPayload
          )
        } else {
          let parentFeedUserId
          if (messageType === 'comment') {
            /* get user id that created the parent_id feed */
            const parentFeed = await findFeedById(parent_id)
            parentFeedUserId = parentFeed.user_id
            if (parentFeedUserId !== user.id) {
              const newCommentOnPostNotificationPayload: any = newMentionNotificationPayload
              delete newCommentOnPostNotificationPayload.message_type
              newCommentOnPostNotificationPayload.email_post_url =
                host + newCommentOnPostNotificationPayload.url
              await knockTriggerWorkflow(
                user.membership_id,
                KNOCK_WORKFLOW_ENUM.new_comment,
                user.id,
                [parentFeedUserId],
                newCommentOnPostNotificationPayload
              )
            }
          }

          // If user that created the post also in the mention list, then remove from the mentioned users to not have 2 notifications.
          const filteredMentionedUsersIds = mentionedUsersIds.filter(
            (id) => id !== parentFeedUserId
          )
          if (filteredMentionedUsersIds.length > 0) {
            await knockTriggerWorkflow(
              user.membership_id,
              KNOCK_WORKFLOW_ENUM.new_mention_notification,
              user.id,
              mentionedUsersIds,
              newMentionNotificationPayload
            )
          }
        }

        if (result.user) {
          // TODO: Is this just a safety check?
          delete result.user.password
        }

        return res.status(201).send({
          success: true,
          data: result,
        })
      } else {
        res.status(500).json(errorHandler(result, 'Feed'))
      }
    } catch (err: any) {
      console.log(err)
      console.log('err ========', err.message)
      sentryCaptureException(err)
      res.status(500).json(errorHandler(err, 'Feed'))
    }
  })

export default handler
