import { updateMuxAssetMasterAccess, getMuxAsset } from '@/memberup/libs/mux'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const {
      body: { assetId },
    } = req

    // Fetch video URL
    await updateMuxAssetMasterAccess(assetId, 'temporary')
    const asset = await getMuxAsset(assetId)
    const playbackId = asset.playback_ids.find((id) => id.policy === 'public').id
    const url = `https://stream.mux.com/${playbackId}.m3u8`
    await updateMuxAssetMasterAccess(assetId, 'none')

    res.json({ success: true, data: { url } })
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Mux video'))
  }
})

export default handler
