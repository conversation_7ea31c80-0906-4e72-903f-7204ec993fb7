import { getMuxAssets } from '@/memberup/libs/mux'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { upload_id } = req.query
    console.error('upload_id', upload_id)
    const assets = await getMuxAssets({ upload_id: upload_id as string })

    return res.status(200).send({
      success: true,
      data: assets,
    })
  } catch (err: any) {
    sentryCaptureException(err)
    return res.status(400).json(errorHandler(err, 'Mux'))
  }
})

export default handler
