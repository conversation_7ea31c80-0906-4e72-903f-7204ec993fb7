import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createLive, findLives } from '@memberup/shared/src/libs/prisma/live'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const result = await findLives({
        where: { membership_id: user.current_membership_id, ...(where || {}) },
        take,
        select,
        skip,
        orderBy,
      })
      return res.json({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Live'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const dbLive = await createLive({
        data: {
          ...req.body,
          membership: {
            connect: {
              id: user.current_membership_id,
            },
          },
          user: {
            connect: {
              id: user.id,
            },
          },
        },
      })
      return res.json({ success: true, data: dbLive })
    } catch (err: any) {
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'Live'))
    }
  })

export default handler
