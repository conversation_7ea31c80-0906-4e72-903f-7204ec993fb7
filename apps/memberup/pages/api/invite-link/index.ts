import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createInviteLink, findInviteLinks } from '@memberup/shared/src/libs/prisma/invite-link'
import { findMembershipById } from '@memberup/shared/src/libs/prisma/membership'
import { findMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { findNumOfUsers } from '@memberup/shared/src/libs/prisma/user'
import { IMembership } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { USER_STATUS_ENUM } from '@/shared-types/enum'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN
const INVITE_TOKEN_SECRET = process.env.INVITE_TOKEN_SECRET

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const result = await findInviteLinks({
        where: {
          ...((where as any) || {}),
          membership_id: user.current_membership_id as string,
        },
        take,
        select,
        skip,
        orderBy: [{ createdAt: 'desc' }],
      })

      const promises = []

      for (const inviteLink of result.docs) {
        promises.push(
          new Promise((resolve) => {
            findNumOfUsers({
              where: { invite_token: inviteLink.token, status: USER_STATUS_ENUM.active },
            })
              .then((res) => {
                inviteLink['members'] = res
              })
              .catch((err) => {})
              .finally(() => {
                resolve(true)
              })
          }),
        )
      }

      Promise.all(promises).then(() => {
        res.json({ success: true, data: { total: result.total, docs: result.docs } })
      })
    } catch (err: any) {
      console.log('err ====', err)
      res.status(400).json(errorHandler(err, 'InviteLink'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { name } = req.body

      if (!name) {
        return res.status(400).json({ message: 'You should provide required fields.' })
      }

      // Fetch the membershipSetting
      const membershipSetting = await findMembershipSetting({
        where: { membership_id: user.current_membership_id },
        include: { membership: true },
      })

      // @ts-ignore
      const existingInviteLinks = await findInviteLinks({
        where: { membership_id: user.current_membership_id as string },
      })

      if (existingInviteLinks.total > 0) {
        return res.status(400).json({ message: 'There are already an Invite Link with the same name.' })
      } else {
        const membership: IMembership = await findMembershipById({
          where: { id: user.current_membership_id },
        })
        const token = jwt.sign({ name }, INVITE_TOKEN_SECRET)
        let domain = `${membership.slug}.${DEFAULT_DOMAIN}`
        if (
          membershipSetting.custom_host &&
          membershipSetting.custom_host_verified &&
          !membershipSetting.custom_host_disabled
        ) {
          domain = membershipSetting.custom_host_redirect || membershipSetting.custom_host
        }
        const url = `https://${domain}/signup?invite_link_token=${token}`
        // @ts-ignore
        const result = await createInviteLink({
          data: {
            active: true,
            url,
            token,
            membership_id: user.current_membership_id as string,
          },
        })
        return res.status(201).send({ success: true, data: { ...result, members: 0 } })
      }
    } catch (err: any) {
      return res.status(400).json(errorHandler(err, 'InviteLink'))
    }
  })

export default handler
