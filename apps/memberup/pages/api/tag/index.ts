import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createTag, findTags } from '@memberup/shared/src/libs/prisma/tag'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const result = await findTags({ where, take, select, skip, orderBy })
      res.json({ success: true, data: result })
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'Tag'))
    }
  })
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { name, active } = req.body
      const result = await createTag({
        data: {
          name,
          active,
          membership: {
            connect: {
              id: user.id,
            },
          },
        },
      })
      if (result?.id) {
        res.json({ success: true, data: result })
      } else {
        res.status(400).json(errorHandler(result, 'Tag'))
      }
    } catch (err: any) {
      res.status(400).json(errorHandler(err, 'Tag'))
    }
  })

export default handler
