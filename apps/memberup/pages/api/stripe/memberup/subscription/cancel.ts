import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeCancelSubscriptionMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  // Delete the subscription
  try {
    const deletedSubscription = await stripeCancelSubscriptionMain(STRIPE_SECRET_KEY, req.body.subscriptionId)
    res.send(deletedSubscription)
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
