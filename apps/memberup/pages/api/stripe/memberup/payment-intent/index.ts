import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400 } from '@/shared-libs/api-utils'
import { stripeCreatePaymentIntent, stripeCreatePaymentIntentMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { created_gte, created_lt, limit, starting_after } = req.query
      const stripeCustomerId = user.membership_setting?.stripe_customer_id

      if (!stripeCustomerId) {
        return res.status(200).send({ success: true, data: { data: [], has_more: false } })
      }
      const params = {
        customer: stripeCustomerId,
        limit: limit ? parseInt(limit as string) : 100,
        starting_after: starting_after as string,
      }

      if (created_gte || created_lt) {
        params['created'] = {
          gte: created_gte,
          lt: created_lt,
        }
      }
      // const result = await stripeGetPaymentIntents(params)

      return status200(res, [])
    } catch (err: any) {
      console.log(err)
      Sentry.captureException(err)
      return status400(res, err.message)
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const stripeCustomerId = user.membership_setting?.stripe_customer_id

      if (!stripeCustomerId) {
        return res.status(200).send({ success: true, data: { data: [], has_more: false } })
      }

      const { amount, currency } = req.body
      const paymentIntent = await stripeCreatePaymentIntentMain(STRIPE_SECRET_KEY, {
        amount,
        customer: stripeCustomerId,
        currency: currency || 'usd',
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'never',
        },
        expand: ['charges'],
      })
      return status200(res, { clientSecret: paymentIntent.client_secret })
    } catch (err: any) {
      console.log(err)
      Sentry.captureException(err)
      return status400(res, err.message)
    }
  })

export default handler
