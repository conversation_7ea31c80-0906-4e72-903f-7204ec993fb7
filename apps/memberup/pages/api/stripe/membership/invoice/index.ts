import authenticationMiddleware from '@/memberup/middlewares/authentication'
import membershipSettingMiddleware from '@/memberup/middlewares/membership-setting'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import <PERSON><PERSON> from 'stripe'
import { stripeGetInvoices } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(membershipSettingMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { created_gte, created_lt, limit, starting_after } = req.body
      const stripeConnectedAccount = user.membership_setting?.stripe_connect_account

      if (!stripeConnectedAccount) {
        return res.status(200).send({ success: true, data: { data: [], has_more: false } })
      }

      const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
      const userProfile = dbUser.profile

      if (!userProfile?.stripe_customer_id) {
        return res.status(200).send({ success: true, data: { data: [], has_more: false } })
      }

      const params: Stripe.InvoiceListParams = {
        customer: userProfile.stripe_customer_id,
        limit: limit ? parseInt(limit as string) : 100,
        starting_after: starting_after as string,
        expand: ['data.charge'],
      }
      if (created_gte || created_lt) {
        params['created'] = {
          gte: created_gte,
          lt: created_lt,
        }
      }

      const result = await stripeGetInvoices(stripeConnectedAccount, params)
      res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      res.status(400).json({ message: err.message })
    }
  })

export default handler
