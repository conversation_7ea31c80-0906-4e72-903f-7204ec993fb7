import authenticationMiddleware from '@/memberup/middlewares/authentication'
import membershipSettingMiddleware from '@/memberup/middlewares/membership-setting'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import { stripeGetCustomertBalanceTransactions } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(membershipSettingMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { limit, ending_before, starting_after } = req.query
      const stripeConnectAccount = user.membership_setting?.stripe_connect_account

      if (!stripeConnectAccount) {
        return res.status(200).send({ success: true, data: [] })
      }

      const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
      const userProfile = dbUser.profile

      if (!userProfile?.stripe_customer_id) {
        return res.status(200).send({ success: true, data: [] })
      }

      const params = {
        limit: limit ? parseInt(limit as string) : 100,
        starting_after: starting_after as string,
        ending_before: ending_before as string,
      }

      const result = await stripeGetCustomertBalanceTransactions(
        stripeConnectAccount,
        userProfile.stripe_customer_id,
        params
      )
      res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json({ message: err.message })
    }
  })

export default handler
