import { findMembership } from '@memberup/shared/src/libs/prisma/membership'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { USER_PLANS } from '@memberup/shared/src/settings/plans'
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import {
  stripeCreatePrice,
  stripeCreateProduct,
  stripeGetPrices,
  stripeGetProducts,
} from '@/shared-libs/stripe'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .put(async (req, res) => {
    try {
      const user = req['user']

      const result = await findMembership({
        where: { id: user.current_membership_id },
        include: { membership_setting: true },
      })

      if (
        result.membership_setting.stripe_connect_account &&
        (result.membership_setting.stripe_connect_account.enabled ||
          result.membership_setting.stripe_connect_account.access_token)
      ) {
        return res.status(200).send({
          success: true,
        })
      }

      const stripeConnectAccountInfo = {
        ...result.membership_setting.stripe_connect_account,
        enabled: true,
      }

      const membershipSetting = result.membership_setting

      // Create the Stripe products
      const stripeProducts = await stripeGetProducts(stripeConnectAccountInfo)
      let stripeProduct = stripeProducts.data.find((p) => p.metadata?.membership === result.slug)
      if (!stripeProduct) {
        stripeProduct = await stripeCreateProduct(stripeConnectAccountInfo, {
          name: result.name,
          metadata: {
            membership: result.slug,
          },
        })
      }

      const updateMembershipSettingPayload = {
        stripe_connect_account: stripeConnectAccountInfo,
      } as any

      if (stripeProduct) {
        const existingStripePrices = await stripeGetPrices(stripeConnectAccountInfo, {
          product: stripeProduct.id,
        })

        const stripePrices = (existingStripePrices?.data || []).map((p) => ({
          id: p.id,
          active: p.active,
          currency: p.currency,
          livemode: p.livemode,
          unit_amount: p.unit_amount,
          recurring: p.recurring,
          metadata: p.metadata,
          type: p.type,
        }))

        for (const userPlan of USER_PLANS) {
          let temp = stripePrices.find(
            (p) =>
              p.metadata?.planType === userPlan.type ||
              (p.recurring?.interval || null) === (userPlan.recurringInterval || null)
          )
          if (!temp) {
            temp = await stripeCreatePrice(stripeConnectAccountInfo, {
              unit_amount: userPlan.price * 100,
              currency: 'usd',
              active: false,
              recurring:
                userPlan.type !== 'one_time'
                  ? { interval: userPlan.recurringInterval as any }
                  : undefined,
              product: stripeProduct.id,
              metadata: {
                name: userPlan.name,
                title: userPlan.title,
                planType: userPlan.planType,
                description: userPlan.description,
              },
            })
            if (temp) {
              stripePrices.push({
                id: temp.id,
                active: temp.active,
                currency: temp.currency,
                livemode: temp.livemode,
                unit_amount: temp.unit_amount || 0,
                recurring: temp.recurring,
                metadata: temp.metadata,
                type: temp.type,
              })
            }
          } else {
            temp.active = false
          }
        }
        updateMembershipSettingPayload['stripe_prices'] = stripePrices
      }

      await updateMembershipSetting({
        where: { id: membershipSetting.id },
        data: updateMembershipSettingPayload,
      })

      return res.status(200).send({
        success: true,
      })
    } catch (err: any) {
      sentryCaptureException(err)
      console.error(err)
      return res.status(200).send({
        success: false,
        message: err.message,
      })
    }
  })

export default handler
