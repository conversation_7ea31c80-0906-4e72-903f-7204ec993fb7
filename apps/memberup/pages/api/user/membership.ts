import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { errorHand<PERSON> } from '@memberup/shared/src/libs/prisma/error-handler'
import { findMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { findUser, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import { createStreamChatUserToken } from '@memberup/shared/src/libs/stream-chat'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import { stripeAttachPaymentMethodToCustomer, stripeCreateCustomer } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    if (!user.membership_id) {
      return res.status(400).json({
        message: 'Membership is not configured properly. Please contact the owner.',
      })
    }

    const membershipSetting = await findMembershipSetting({
      where: { membership_id: user.membership_id },
    })
    if (
      !membershipSetting?.stripe_connect_account?.access_token &&
      !membershipSetting?.stripe_connect_account?.enabled
    ) {
      return res.status(400).json({
        message: 'Membership is not configured properly. Please contact the owner.',
      })
    }

    const stripeConnectAccountInfo = membershipSetting.stripe_connect_account
    const { name, paymentMethodId } = req.body
    const names = (name || '').split(' ')
    let result: IUser = await findUser({
      where: { email: user.email, membership_id: user.membership_id },
      include: {
        profile: true,
      },
    })

    if (!result?.id) {
      return res.status(400).end('Failed to start community.')
    }

    const userProfile = result.profile
    const userId = result.id
    result = await updateUser({
      where: { id: userId },
      data: {
        first_name: names[0],
        last_name: names[1],
      },
      include: {
        profile: true,
      },
    })

    const stripeCustomer = await stripeCreateCustomer(stripeConnectAccountInfo, {
      email: user.email,
    })
    const updateUserProfilePayload = { stripe_customer_id: stripeCustomer.id }

    const attachedPaymentMethod = await stripeAttachPaymentMethodToCustomer(
      stripeConnectAccountInfo,
      paymentMethodId,
      stripeCustomer.id
    )

    updateUserProfilePayload['stripe_payment_method_id'] = attachedPaymentMethod.id

    // const temp = {
    //   'custom:stripe_customer_id': stripeCustomer.id,
    //   'custom:payment_method_id': attachedPaymentMethod.id,
    //   'custom:user_id': userId,
    //   'custom:user_role': result.role,
    // }
    // if (names[0]) {
    //   temp['given_name'] = names[0]
    // }
    // if (names[1]) {
    //   temp['family_name'] = names[1]
    // }

    if (Object.keys(updateUserProfilePayload).length) {
      await updateUserProfile({ where: { id: userProfile.id }, data: updateUserProfilePayload })
    }

    const streamChatUserToken = await createStreamChatUserToken(userId)

    return res.status(200).send({
      success: true,
      data: {
        user: {
          ...result,
          id: userId,
          membership: undefined,
        },
        profile: userProfile,
        streamChatUserToken,
      },
    })
  } catch (err: any) {
    console.log('postMembershipError ====', err)
    res.status(400).json(errorHandler(err, 'User'))
  }
})

export default handler
