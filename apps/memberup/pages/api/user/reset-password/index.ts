import { hashPassword } from '@memberup/shared/src/libs/bcrypt'
import { updateUser } from '@memberup/shared/src/libs/prisma/user'
import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

const RESET_PASSWORD_TOKEN_SECRET = process.env.RESET_PASSWORD_TOKEN_SECRET

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  const { token, password } = req.body
  try {
    const decodedToken = jwt.verify(token, RESET_PASSWORD_TOKEN_SECRET)
    if (!decodedToken['user_id']) {
      return res.status(400).json({ message: 'Invalid token' })
    }

    const hashedPassword = await hashPassword(password)
    await updateUser({ where: { id: decodedToken['user_id'] }, data: { password: hashedPassword } })
    res.json({
      success: true,
    })
  } catch (err) {
    console.error(err)
    res.json({
      success: false,
    })
  }
})

export default handler
