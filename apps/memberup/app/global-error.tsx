'use client'

// Captures errors in root layout

import { useEffect } from 'react'
import * as Sentry from '@sentry/nextjs'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    Sentry.captureException(error)
  }, [error])

  return (
    <html lang="en">
      <body className="app-router dark h-full font-sans">
        <main className="flex h-screen w-screen flex-col items-center justify-center space-y-4">
          <h1 className="text-2xl">Internal Server Error</h1>
          <button
            onClick={() => reset()}
            className="h-[50px] rounded-md bg-orange-600 px-4 py-2 text-gray-600 transition-all delay-75 hover:bg-orange-500 disabled:opacity-50"
          >
            Try Again
          </button>
        </main>
      </body>
    </html>
  )
}
