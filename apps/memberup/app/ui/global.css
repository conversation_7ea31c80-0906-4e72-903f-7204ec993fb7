@import 'preflight.css';
@import 'fonts.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

input.active:is(:autofill, :-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px hsl(var(--white-500)) inset !important;
}

input:is(:autofill, :-webkit-autofill) {
  transition:
    background-color 999999999s linear,
    color 999999999s linear !important;
  background-clip: content-box !important;
  outline: none !important;
}

input:is(:autofill, :-webkit-autofill):not(:disabled) {
  -webkit-text-fill-color: hsl(var(--black-700)) !important;
}

input:is(:autofill, :-webkit-autofill):disabled,
body.dark input:is(:autofill, :-webkit-autofill):disabled {
  -webkit-text-fill-color: hsl(var(--grey-500)) !important;
}

body.dark input:is(:autofill, :-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px hsl(var(--black-500)) inset !important;
  color: hsl(var(--white-500)) !important;
  background: hsl(var(--black-500)) !important;
  caret-color: hsl(var(--white-500)) !important;
}

body.dark .variant-transparent input:is(:autofill, :-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px #1d1b26 inset !important;
}

body.dark input:is(:autofill, :-webkit-autofill):not(:disabled),
body.dark input:is(:autofill, :-webkit-autofill):not(:disabled):focus,
body.dark input:is(:autofill, :-webkit-autofill):not(:disabled):hover,
body.dark input:is(:autofill, :-webkit-autofill):not(:disabled):active {
  -webkit-text-fill-color: #fff !important;
  color: #fff !important;
}

@layer base {
  :root {
    /* MemberUp colors */
    --pure-black: 0 0% 0%; /* #00000 */
    --pure-white: 0 0% 100%; /* #FFFFFF */
    --body-copy: 180 1% 89%; /* # */
    --button-focus: 210 5% 86%; /* # */
    --dark-background-1: 0 0% 4%; /* #0A0A0A */
    --dark-background-2: 240 5% 10%; /* #17171A */
    --dark-background-3: 240 4% 14%; /* #212124 */
    --font-light-ui-black: 0 5% 4%; /* # */
    --font-light-ui-gray: 220 5% 36%; /* # */
    --light-background-200: 180 9% 96%; /* # */
    --spark-gray: 0 0% 63%; /* # */
    --surface-1: 240 9% 9%; /* # */
    --surface-1-rgb: 240 9% 9%; /* # */
    --ui-dark-100: 180 11% 98%; /* #FAFBFB */
    --ui-dark-200: 210 13% 97%; /* #F6F7F8 */
    --ui-dark-300: 220 12% 95%; /* #F1F2F4 */
    --ui-dark-400: 220 9% 94%; /* #EDEEF0 */
    --ui-dark-500: 220 9% 94%; /* #E3E5E9 */
    --ui-dark-600: 220 11% 85%; /* ##D3D6DC */
    --ui-dark-700: 220 11% 84%; /* #D1D4DA */
    --ui-dark-800: 219 11% 76%; /* #BABFC8 */
    --ui-dark-900: 219 11% 65%; /* #9BA2AF */
    --ui-dark-1000: 221 11% 60%; /* #8D94A3 */
    --ui-light-300: 240 5% 93%; /* #EBEBED */
    --ui-light-800: 218 4% 62%; /* #9B9EA3 */
    --ui-light-1000: 219 7% 37%; /* #585D66 */
    --tertiary-grey: 221 8% 51%; /* #797F8C */

    --gray-1: 240 5% 12%; /* #1C1C1F */
    --gray-2: 220 12% 18%; /* #292B2F */
    --gray-3: 227 13% 26%; /* #3A3C43 */
    --gray-4: 221 13% 64%; /* #8B94A3 */
    --gray-5: 240 6% 10%; /* #202125 */
    --gray-6: 240 6% 10%; /* #202125 */
    --gray-7: 228 6% 17%; /* #292a2e */

    --black: 0 0% 0%; /* #000000 */
    --black-100: 221 11% 60%; /* #8D94A3 */
    --black-200: 221 8% 51%; /* #797F8C */
    --black-300: 225 6% 13%; /* #202124 */
    --black-400: 214 10% 14%; /* #202327 */
    --black-500: 220 12% 10%; /* #16181C */
    --black-600: 240 6% 10%; /* #17171A */
    --black-700: 0 0% 3%; /* #080808 */
    --white-100: 225 17% 95%; /* #F1F2F5 */
    --white-200: 180 2% 88%; /* #E0E1E1 */
    --white-300: 0 0% 92%; /* #EBEBEB */
    --white-400: 180 9% 96%; /* #F3F5F5 */
    --white-500: 0 0% 100%; /* #FFFFFF */
    --grey-100: 0 0% 92%; /* #EBEBEB */
    --grey-200: 240 4% 90%; /* #E5E5E7 */
    --grey-300: 0 0% 88%; /* #E0E0E0 */
    --grey-400: 0 0% 78%; /* #C7C7C7 */
    --grey-500: 0 0% 62%; /* #9E9E9E */
    --grey-600: 0 0% 60%; /* #999999 */
    --grey-700: 0 0% 40%; /* #666666 */
    --grey-800: 230 6% 19%; /* #2E2F34 */
    --grey-900: 0 0% 13%; /* #212121 */
    --green-100: 97 66% 73%; /* #AEE78B */
    --green-200: 97 95% 37%; /* #48B705 */
    --red-100: 0 55% 50%; /* #C43939 */
    --red-200: 0 88% 61%; /* #F34646 */
    --orange-100: 33 100% 66%; /* #FFB153 */
    --primary-100: 255 100% 66%; /* #7D52FF */
    --primary-200: 255.16 78.45% 54.51%; /* #5E30E6 */
    --primary-300: 269 98% 41%; /* #6402CE */
    --primary-400: 285 100% 66%; /* #D452FF */

    --legacy-gray-1: 220.9 10.7% 59.6%; /* #8D94A3 */
    --legacy-gray-2: 0 0 27.5%; /* #464646 */
    --legacy-white-transparent-80: 0 0% 100% / 80%; /* #FFFC */

    /* Gradients */
    --primary-gradient: linear-gradient(90deg, hsl(var(--primary-100)) 0%, hsl(var(--primary-400)) 100%);
    --primary-gradient-10-dark: linear-gradient(90deg, #130f20 0%, #1c0f20 100%);
    --primary-gradient-10-light: linear-gradient(90deg, #e5e2f6 0%, #ede2f6 100%);

    /* Component-specific */
    --divider-border-color: 221 11% 60% 16;
    --component-border-color: 221 11% 60% 16%;

    /* shadcn colors */
    --background: var(--white);
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: var(--pure-white);
    --popover-foreground: var(--ui-dark-1000);

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    /* Paddings & margins */
    --container-border-radius: 0.625rem;
    --container-padding: 1.5rem;
    --page-container-padding: 1rem;
    --md-page-container-padding: 1.25rem;
    --mobile-page-container-padding: 1rem;

    /* Radiuses */
    --radius: 0.5rem;
  }

  .dark {
    --background: var(--dark-background-1);
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: var(--dark-background-2);
    --popover-foreground: var(--ui-dark-1000);

    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .padded-content-container {
    @apply md:px-[var(--md-page-container-padding)];
  }

  .mobile-padded-content-container {
    @apply max-md:px-[var(--mobile-page-container-padding)];
  }

  .content-container {
    @apply w-full md:max-w-[1084px] xl:max-w-[1378px];
  }

  .rounded-container {
    @apply rounded-[var(--container-border-radius)] bg-pure-white dark:bg-black-500;
  }

  .rounded-box {
    @apply rounded-[var(--container-border-radius)] bg-pure-white dark:bg-black-500;
  }

  .container-padding {
    padding: var(--container-padding);
  }

  .tailwind-component {
    /* Temporary class that should be applied to Tailwind-based components */
    @apply font-sans;
  }

  .square {
    height: 0;
    position: relative;
    overflow: hidden;
    padding-top: 100%;
    width: 100%;
  }

  .page-inner-px {
    @apply px-[var(--page-container-padding)] md:px-[var(--md-page-container-padding)];
  }

  .page-inner-py {
    @apply py-[var(--page-container-padding)] md:py-[var(--md-page-container-padding)];
  }

  .page-inner-pt {
    @apply pt-[var(--page-container-padding)] md:pt-[var(--md-page-container-padding)];
  }

  .page-inner-pb {
    @apply pb-[var(--page-container-padding)] md:pb-[var(--md-page-container-padding)];
  }

  .page-inner-mb {
    @apply mb-[var(--page-container-padding)] md:mb-[var(--md-page-container-padding)];
  }

  .bg-primary-gradient {
    background: var(--primary-gradient);
  }

  .bg-primary-gradient-10-dark {
    background: var(--primary-gradient-10-dark);
  }

  .bg-primary-gradient-10-light {
    background: var(--primary-gradient-10-light);
  }

  .rendered-editor-text {
    @apply leading-6;

    ul,
    ol {
      @apply my-5 pl-4;
    }

    ul {
      @apply list-disc;
    }

    ol {
      @apply list-decimal;
    }

    a {
      @apply text-community-primary;
    }

    p:not(:last-child) {
      @apply mb-2;
    }
  }
}

@layer utilities {
  .cursor-pencil {
    cursor: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10.539.937a3.204 3.204 0 014.525 0 3.204 3.204 0 01.134 4.383l-.134.142-8.853 8.854a1.604 1.604 0 01-.605.398l-.174.06-4.156 1.187c-.705.201-1.371-.422-1.247-1.133l.025-.107 1.187-4.157c.067-.236.181-.455.335-.644l.123-.136L10.54.937zm3.11 1.414a1.604 1.604 0 00-1.599-.087l-.097.087-.494.493 1.697 1.696-1.414 1.414-1.696-1.697-6.91 6.91-.68 2.377 2.378-.68 8.815-8.816a1.604 1.604 0 00.168-1.487l-.08-.112z' fill='%238d94a3'/%3E%3C/svg%3E"),
      pointer;
  }
}
