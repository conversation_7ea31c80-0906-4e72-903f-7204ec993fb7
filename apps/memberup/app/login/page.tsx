'use client'

import { useSearchParams } from 'next/navigation'
import { useState } from 'react'

import { LoginForm } from '@/components/auth/login-form'
import { PasswordResetForm } from '@/components/auth/password-rest-form/password-reset-form'
import { ParticlesContainer } from '@/components/layout/particles-container'
import { PrimaryBlurredBackground } from '@/components/layout/primary-blurred-background'
import { ScrollArea } from '@/components/ui'
import useSwitchCommunity from '@/hooks/useSwitchCommunity'
import { IAuthenticatedUser, IUserMembership } from '@/shared-types/interfaces'

const DEFAULT_COMMUNITY = 'community'

const Login = () => {
  const searchParams = useSearchParams()
  const nextPath = searchParams.get('next')
  const switchCommunity = useSwitchCommunity()
  const [passwordReset, setPasswordReset] = useState(false)

  const handleSuccess = (user: IAuthenticatedUser) => {
    // If there's a next path, it will be handled by the LoginForm's internal redirect logic
    // Otherwise, switch to the default community
    if (!nextPath) {
      switchCommunity(DEFAULT_COMMUNITY, user.user_memberships as IUserMembership[])
    }
  }

  return (
    <ScrollArea className="absolute h-full w-full bg-black-700">
      <div className="flex h-full min-h-svh w-full items-center justify-center overflow-hidden">
        <div className="relative w-full max-w-[498px] px-3">
          <PrimaryBlurredBackground className="-top-52" />
          <ParticlesContainer className="relative z-20 my-10 w-full rounded-base border border-grey-900 px-4 py-11 sm:px-8">
            {passwordReset ? (
              <PasswordResetForm backToLogin={() => setPasswordReset(false)} />
            ) : (
              <LoginForm
                mode="standalone"
                onForgotPasswordClick={() => setPasswordReset(true)}
                onSuccess={(data) => handleSuccess(data)}
              />
            )}
          </ParticlesContainer>
          <PrimaryBlurredBackground className="-bottom-[32rem]" />
        </div>
      </div>
    </ScrollArea>
  )
}

export default Login
