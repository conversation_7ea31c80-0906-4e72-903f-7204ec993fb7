import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { useParams } from 'next/navigation'
import { useState } from 'react'

import { HorizontalNavigationMenu } from './HorizontalNavigationMenu'
import { HorizontalNavigationMenuItem } from './HorizontalNavigationMenuItem'
import { AdminSettings } from '@/components/settings/admin-settings/AdminSettings'
import { MemberSettings } from '@/components/settings/member-settings'
import { useStore } from '@/hooks/useStore'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'

export function CommunityHorizontalNavigationMenu() {
  const params = useParams()

  const communityURL = (path: string) => `/${params.slug}${path}`
  const [settingsModalOpen, setSettingsModalOpen] = useState(false)
  const user = useStore((state) => state.auth.user)
  const { isCurrentUserAdmin } = useCheckUserRole()

  return (
    <>
      <HorizontalNavigationMenu>
        <HorizontalNavigationMenuItem href={communityURL('')}>Community</HorizontalNavigationMenuItem>
        <HorizontalNavigationMenuItem href={communityURL('/content')}>Content</HorizontalNavigationMenuItem>
        <HorizontalNavigationMenuItem href={communityURL('/events')}>Events</HorizontalNavigationMenuItem>
        <HorizontalNavigationMenuItem href={communityURL('/members')}>Members</HorizontalNavigationMenuItem>
        <HorizontalNavigationMenuItem href={communityURL('/about')}>About</HorizontalNavigationMenuItem>
        {user && (
          <HorizontalNavigationMenuItem onClick={() => setSettingsModalOpen(true)}>
            Settings
          </HorizontalNavigationMenuItem>
        )}
      </HorizontalNavigationMenu>
      <VisuallyHidden>
        {isCurrentUserAdmin ? (
          <AdminSettings
            open={settingsModalOpen}
            onOpenChange={() => setSettingsModalOpen(false)}
            defaultSection="general"
          />
        ) : (
          <MemberSettings
            open={settingsModalOpen}
            onOpenChange={() => setSettingsModalOpen(false)}
            defaultSection="membership"
          />
        )}
      </VisuallyHidden>
    </>
  )
}
