import { Carousel, CarouselContent } from '@/components/ui/carousel'
import { cn } from '@/lib/utils'

export function HorizontalNavigationMenu({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <div className={cn('flex w-full justify-center', className)}>
      <nav className="content-container pb-3">
        <Carousel>
          <CarouselContent className="-ml-1.5 pl-4">{children}</CarouselContent>
        </Carousel>
      </nav>
    </div>
  )
}
