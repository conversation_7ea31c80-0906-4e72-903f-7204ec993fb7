import { useFilePicker } from 'use-file-picker'
import { ImageDimensionsValidator } from 'use-file-picker/validators'

import { Delete20Icon, Photo20Icon, PhotoAdd24Icon } from '@/components/icons'
import { AspectRatio, Button } from '@/components/ui'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { CroppedImage } from '@/components/images/cropped-image'
import { ImageCropper } from '@/components/images/image-cropper'
import { CropArea } from '@/shared-types/types'

function CoverPictureInput({
  disabled,
  onRemovePicture,
  onSelectPicture,
  user,
}: {
  onRemovePicture: () => void
  onSelectPicture: (image: string, cropArea: CropArea, file: any) => void
  disabled?: boolean
  user: any
}) {
  const minWidth = 800
  const minHeight = 450

  const { clear, errors, openFilePicker, plainFiles } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL',
    validators: [
      new ImageDimensionsValidator({
        minWidth,
        minHeight,
      }),
    ],
  })

  const fullName = getFullName(user.first_name, user.last_name)

  const imageCropperSettings =
    plainFiles?.[0]?.type?.indexOf('image') >= 0
      ? {
          url: URL.createObjectURL(plainFiles[0]),
          file: plainFiles[0],
          crop_area: {
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        }
      : null

  const onCropComplete = (e: any) => {
    clear()
    onSelectPicture(e.url, e.cropArea, e.file)
  }

  return (
    <>
      <AspectRatio
        ratio={1 / 0.55}
        className="bg-white-100 dark:bg-black-300 rounded-xl overflow-hidden"
        onClick={() => !user.profile.cover_image && openFilePicker()}
      >
        {user.profile.cover_image ? (
          <>
            <CroppedImage
              src={user.profile.cover_image}
              cropArea={user.profile.cover_image_crop_area}
              width={402}
              height={221}
              alt={`${fullName} cover photo`}
            />
            <div className="absolute top-4 right-4 flex space-x-4">
              <Button
                className="bg-black-700 hover:bg-black-600 dark:hover:bg-black-600"
                size="sm"
                variant="default"
                disabled={disabled}
                onClick={(e) => {
                  e.preventDefault()
                  openFilePicker()
                }}
              >
                <Photo20Icon className="mr-0.5" />
                Change
              </Button>
              <Button
                className="bg-black-700 hover:bg-black-600"
                variant="outline-destructive"
                shape="circular"
                title="Remove"
                size="sm"
                disabled={disabled}
                onClick={(e) => {
                  e.preventDefault()
                  onRemovePicture()
                }}
              >
                <Delete20Icon />
              </Button>
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center w-full h-full select-none cursor-pointer">
            <div className="flex flex-col items-center text-sm">
              <PhotoAdd24Icon className="mb-4 text-black-100" />
              <div className="font-medium text-black-700 dark:text-white-500">
                Upload Cover Photo
              </div>
              <div className="text-primary-100 font-semibold mt-3.5">Select image</div>
            </div>
          </div>
        )}
      </AspectRatio>
      {imageCropperSettings && (
        <ImageCropper
          aspectRatio={minWidth / minHeight}
          url={imageCropperSettings.url}
          open={true}
          file={imageCropperSettings.file}
          minWidth={minWidth}
          minHeight={minHeight}
          onApply={onCropComplete}
          onClose={() => clear()}
        />
      )}
      {errors && errors.length > 0 && (
        <p className="text-red-200 text-sm pt-3">
          Please select an image that is at least {minWidth} x {minHeight} pixels.
        </p>
      )}
    </>
  )
}

export { CoverPictureInput }
