import React, { useMemo } from 'react'

import { ThemeProvider, ThemeOptions, createTheme } from '@mui/material/styles'
// import { AppRouterCacheProvider } from '@mui/material-nextjs/v13-appRouter';

import { DefaultThemeOptions } from '@memberup/shared/src/settings/theme'
import { useAppSelector } from '@/memberup/store/store'
import { selectMembershipTheme } from '@/memberup/store/features/membershipSlice'

export default function MUThemeProvider({ children }: { children: React.ReactNode }) {
  const membershipTheme = useAppSelector((state) => selectMembershipTheme(state))

  const theme = useMemo(() => {
    return createTheme(
      (typeof membershipTheme === 'object' ? membershipTheme : DefaultThemeOptions) as ThemeOptions
    )
  }, [membershipTheme])

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>
}
