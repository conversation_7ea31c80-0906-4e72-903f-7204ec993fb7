import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useRef, useState } from 'react'

import { Close24Icon } from '@/components/icons'
import { But<PERSON> } from '@/components/ui'
import { Carousel, CarouselContent, CarouselItem } from '@/components/ui/carousel'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'

export type SettingsModalSection = {
  name: string
  title: string
  component: React.ComponentType<object> | (() => JSX.Element)
}

export type SettingsModalConfig = {
  sections: SettingsModalSection[]
}

const MenuButton = ({
  children,
  className,
  active,
  onClick,
}: {
  children: React.ReactNode
  className?: string
  active: boolean
  onClick: () => void
}) => (
  <Button
    className={cn(
      'block bg-transparent px-6 py-0 text-left text-black-700 transition-colors hover:bg-white-100 dark:text-white-500 dark:hover:bg-black-300',
      active && 'bg-white-100 dark:bg-black-300',
      className,
    )}
    onClick={onClick}
  >
    {children}
  </Button>
)

export function SettingsModal({
  header,
  open,
  onOpenChange: providedOnOpenChange,
  config,
  defaultSection,
}: {
  header: React.ReactNode
  open: boolean
  onOpenChange: (value: boolean) => void
  config: SettingsModalConfig
  defaultSection?: string
}) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const hasSettingsParam = searchParams.has('settings')
  let settingsParam = searchParams.get('settings')
  settingsParam =
    settingsParam && config.sections.map((s: SettingsModalSection) => s.name).includes(settingsParam)
      ? settingsParam
      : null

  const selectedDefaultSection = settingsParam || defaultSection || config.sections[0].name
  const [activeSection, setActiveSection] = useState(selectedDefaultSection)
  const isOpen = open
  const settingsInitializedRef = useRef(false)

  const sectionConfig = config.sections.find((section: SettingsModalSection) => section.name === activeSection)

  const SectionComponent = sectionConfig?.component

  const onOpenChange = () => {
    if (hasSettingsParam) {
      router.replace(pathname)
    }
    providedOnOpenChange(false)
  }

  useEffect(() => {
    if (hasSettingsParam && !open && !settingsInitializedRef.current) {
      settingsInitializedRef.current = true
      providedOnOpenChange(true)
    }
  }, [hasSettingsParam, providedOnOpenChange, open])

  const sectionComponentClassName =
    'shrink-0 w-full h-full absolute l-0 t-0 pl-5 pt-5 pr-5 pb-5 grow md:p-8 md:border-l-2 md:border-l-grey-200 md:dark:border-l-grey-900'

  return (
    <Dialog open={isOpen}>
      <DialogTitle>
        <VisuallyHidden>Settings</VisuallyHidden>
      </DialogTitle>
      <DialogContent
        className="tailwind-component z-[1000] flex h-full max-h-full max-w-full flex-col dark:bg-black-500 md:h-[606px] md:max-h-[calc(100%_-_6rem)] md:max-w-[calc(900px-10rem)]"
        variant="plain"
      >
        <div className="settings-modal-header flex h-24 items-center border-b border-grey-200 pl-5 pr-5 dark:border-grey-900 md:h-28 md:border-b-2 md:pl-8 md:pr-8">
          <div className="flex flex-grow items-center">{header}</div>
          <div className="flex h-full flex-col justify-center">
            <Button
              className="flex h-10 w-10 items-center justify-center rounded-full bg-white-200 text-black-200 transition-colors hover:bg-grey-400 dark:bg-black-700 dark:text-black-100 dark:hover:bg-grey-900"
              variant="inline"
              onClick={onOpenChange}
            >
              <Close24Icon />
            </Button>
          </div>
        </div>
        <div className="settings-modal-content flex flex-1 flex-col overflow-hidden md:flex-row">
          <ScrollArea className="hidden shrink-0 md:flex">
            <nav className="p-8">
              <ul className="flex flex-col space-y-3">
                {config.sections.map((section: SettingsModalSection) => (
                  <li key={section.name}>
                    <MenuButton
                      className="h-10 w-48 text-base"
                      active={activeSection === section.name}
                      onClick={() => setActiveSection(section.name)}
                    >
                      {section.title}
                    </MenuButton>
                  </li>
                ))}
              </ul>
            </nav>
          </ScrollArea>
          <div className="shrink border-b border-grey-200 py-5 pl-2 dark:border-grey-900 md:hidden">
            <Carousel>
              <CarouselContent>
                {config.sections.map((section: SettingsModalSection) => (
                  <CarouselItem key={section.name} className="basis-auto">
                    <MenuButton
                      className="h-8 text-sm"
                      active={activeSection === section.name}
                      onClick={() => setActiveSection(section.name)}
                    >
                      {section.title}
                    </MenuButton>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          </div>
          <div className="relative h-full w-full">
            <ScrollArea className={sectionComponentClassName}>
              <SectionComponent />
            </ScrollArea>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
