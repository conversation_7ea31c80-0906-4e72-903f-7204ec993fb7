import type { <PERSON>a, <PERSON>Obj } from '@storybook/react'
import { toast } from '@/components/ui/sonner'

import { Toaster } from '@/components/ui/sonner'
import { Button } from '../button'

const meta: Meta = {
  title: 'UI/Sonner',
  parameters: {
    layout: 'centered',
  },
  args: {
    action: () => {},
  },
}

export default meta

type Story = StoryObj<typeof meta>

const SonnerTrigger = ({ action }: { action: () => void }) => {
  return (
    <Button variant="default" onClick={action}>
      Trigger Sonner
    </Button>
  )
}

const ModalTemplate: Story = {
  render: (args) => {
    return (
      <>
        <SonnerTrigger action={args.action} />
        <Toaster />
      </>
    )
  },
}

// export const Default: Story = {
//   ...ModalTemplate,
//   args: {
//     action: () =>
//       toast.default('Event has been created', {
//         description: 'Sunday, December 03, 2037 at 9:00 AM',
//         action: {
//           label: 'Undo',
//           onClick: () => {},
//         },
//       }),
//   },
// }

export const Success: Story = {
  ...ModalTemplate,
  args: {
    action: () => toast.success('Event has been created'),
  },
}

export const Error: Story = {
  ...ModalTemplate,
  args: {
    action: () => toast.error('An error has occurred'),
  },
}

export const Info: Story = {
  ...ModalTemplate,
  args: {
    action: () => toast.info('The wait time seems to be longer than usual'),
  },
}

// export const Action: Story = {
//   ...ModalTemplate,
//   args: {
//     action: () =>
//       toast('Event has been created', {
//         action: {
//           label: 'Undo',
//           onClick: () => {},
//         },
//       }),
//   },
// }
