import React, { useState } from 'react'
import { VisibilityOff20Icon, VisibilityOn20Icon } from '@/components/icons'
import { Input, InputProps } from './Input'
import { Button } from '../button'
import { cn } from '@/lib/utils'

export interface PasswordInputProps extends InputProps {}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>((props, ref) => {
  const [showPassword, setShowPassword] = useState(false)
  const iconClassName = cn(
    'relative z-10 text-black-200 absolute left-0 top-0',
    props.disabled ? 'text-grey-500' : 'dark:text-black-100'
  )

  const onButtonClick = (event: any) => {
    event.preventDefault()

    if (props.disabled) return

    setShowPassword(!showPassword)
  }

  return (
    <Input
      append={
        <Button
          variant="inline"
          className={cn(
            'px-4 flex flex-col justify-center',
            !props.disabled && '[&:hover_svg]:text-black-100 [&:hover_svg]:dark:text-black-200',
            props.disabled && 'cursor-not-allowed'
          )}
          onClick={onButtonClick}
        >
          <div className="w-5 h-5 relative">
            <VisibilityOn20Icon
              className={cn(iconClassName, showPassword ? 'opacity-1' : 'opacity-0')}
            />
            <VisibilityOff20Icon
              className={cn(iconClassName, showPassword ? 'opacity-0' : 'opacity-1')}
            />
          </div>
        </Button>
      }
      {...props}
      ref={ref}
      type={showPassword ? 'text' : 'password'}
    />
  )
})

PasswordInput.displayName = 'PasswordInput'

export { PasswordInput }
