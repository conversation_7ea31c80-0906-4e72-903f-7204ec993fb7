import * as React from 'react'

import { Control } from '@/components/ui/control/Control'
import { cn } from '@/lib/utils'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  inputClassName?: string
  rightContent?: React.ReactNode
  error?: boolean
  prepend?: React.ReactNode
  append?: React.ReactNode
  legacyBackground?: boolean
  variant?: 'default' | 'dialog'
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      error = false,
      disabled = false,
      inputClassName,
      placeholder,
      prepend,
      append,
      legacyBackground,
      type,
      value,
      variant,
      onFocus,
      onBlur,
      rightContent,
      ...props
    },
    ref
  ) => {
    const [isFocused, setIsFocused] = React.useState(false)
    const active = isFocused || Boolean(value)

    return (
      <Control
        active={active}
        className={className}
        disabled={disabled}
        rightContent={rightContent}
        error={error}
        focused={isFocused}
        placeholder={placeholder}
        legacyBackground={legacyBackground}
        type="input"
        variant={variant}
      >
        {prepend}
        <input
          type={type}
          className={cn(
            'flex h-11 mx-4 w-full bg-transparent py-1 text-sm border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none disabled:cursor-not-allowed relative z-20',
            prepend && 'pl-0',
            disabled ? 'text-grey-500 dark:text-grey-500' : 'text-black-700 dark:text-white-500',
            legacyBackground && 'legacy-background',
            inputClassName,
            active && 'active'
          )}
          disabled={disabled}
          ref={ref}
          value={value}
          onBlur={(e) => {
            setIsFocused(false)
            onFocus && onFocus(e)
          }}
          onFocus={(e) => {
            setIsFocused(true)
            onBlur && onBlur(e)
          }}
          {...props}
        />
        {append}
      </Control>
    )
  }
)
Input.displayName = 'Input'

export { Input }
