'use client'

import <PERSON><PERSON><PERSON> from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import React, { useEffect, useState } from 'react'
import { useChatContext } from 'stream-chat-react'

import { updateFeedApi } from '@memberup/shared/src/services/apis/feed.api'
import { CHANNEL_TYPE_ENUM, FEED_STATUS_ENUM, FEED_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { PostSummary } from '@/components/feed/post-summary'
import { Button, SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { useStore } from '@/hooks/useStore'
import { showToast } from '@/shared-libs/toast'

export function ModerationSettings() {
  const [isLoading, setIsLoading] = useState(true)
  const [isRequesting, setIsRequesting] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const [reportedPosts, setReportedPosts] = useState([])
  const { client: streamChatClient } = useChatContext()
  const [messages, setMessages] = useState({
    previous: undefined,
    next: undefined,
    results: [],
  })
  const [action, setAction] = useState(null)
  const [selectedPost, setSelectedPost] = useState(null)
  const [openConfirm, setOpenConfirm] = useState(false)

  useEffect(() => {
    const initialize = async () => {
      const channelFilters = { team: membership.id, type: CHANNEL_TYPE_ENUM.livestream }
      const messageFilters = {
        feed_type: { $in: [FEED_TYPE_ENUM.default, FEED_TYPE_ENUM.comment] },
        feed_status: { $in: [FEED_STATUS_ENUM.reported] },
      }
      try {
        const res = await streamChatClient.search(channelFilters, messageFilters, {})
        const temp = res.results.map((item) => item.message)
        setReportedPosts(res.results.map((r) => r.message))
        setMessages({
          previous: res.previous,
          next: res.next,
          results: messages.results.concat(temp),
        })
      } catch (e) {
        console.error(e)
      } finally {
        setIsLoading(false)
      }
    }
    initialize()
  }, [streamChatClient])

  const handleConfirmPostUpdate = async () => {
    await handlePostUpdate(
      selectedPost.id,
      action === 'approve' ? FEED_STATUS_ENUM.rejected : FEED_STATUS_ENUM.approved,
    )
  }

  const handleConfirmModalClose = () => {
    setAction(null)
    setSelectedPost(null)
    setOpenConfirm(false)
  }

  const handleApproveButtonClick = (post) => {
    setSelectedPost(post)
    setAction('approve')
    setOpenConfirm(true)
  }

  const handleRejectButtonClick = (post) => {
    setSelectedPost(post)
    setAction('reject')
    setOpenConfirm(true)
  }

  const handlePostUpdate = async (messageId: string, status: FEED_STATUS_ENUM) => {
    // Optimistically remove the post from the reported posts.
    setReportedPosts(reportedPosts.filter((p) => p.id !== messageId))
    setIsRequesting(true)
    const data = {
      feed_status: status,
    }
    try {
      const res = await updateFeedApi(messageId, data)
      if (res.data.success) {
        const message = 'The post has been ' + (status === FEED_STATUS_ENUM.approved ? 'approved' : 'rejected')
        showToast(message, 'success')
      } else {
        showToast('Un unexpected error happened. Please try again.', 'error')
      }
    } catch (e) {
      console.error(e)
      showToast(e.message, 'error')
    } finally {
      setIsRequesting(false)
    }
  }

  return (
    <div className={'space-y-6'}>
      <ConfirmModal
        title={`Are you sure you want to ${action} the post?`}
        onConfirm={handleConfirmPostUpdate}
        open={openConfirm}
        onClose={handleConfirmModalClose}
      />
      <h2 className="text-lg font-semibold text-white-500">Moderation</h2>
      <div className="font-['Graphik'] text-sm font-normal leading-snug text-[#8d94a3]">
        Approve and reject reported posts.
      </div>
      {isLoading && <SkeletonBox />}
      {reportedPosts.map((m) => {
        return (
          <div key={m.id}>
            <PostSummary
              readonly={true}
              feed={m}
              extraHeaderComponents={
                <div className="flex gap-3">
                  <Button
                    size="sm"
                    type="submit"
                    variant="outline"
                    loading={isRequesting && action === 'approve'}
                    disabled={isRequesting}
                    onClick={() => handleApproveButtonClick(m)}
                    data-cy="approval-all-button"
                  >
                    <CheckIcon />
                    Approve
                  </Button>
                  <Button
                    size="sm"
                    type="submit"
                    loading={isRequesting && action === 'reject'}
                    disabled={isRequesting}
                    variant="outline"
                    onClick={() => handleRejectButtonClick(m.message)}
                    data-cy="approval-all-button"
                  >
                    <CloseIcon />
                    Reject
                  </Button>
                </div>
              }
            />
          </div>
        )
      })}
      {!isLoading && reportedPosts.length === 0 && (
        <div className="inline-flex w-full items-center justify-between rounded-[10px] bg-[#202124] px-5 py-10">
          <div className="inline-flex shrink grow basis-0 flex-col items-center justify-center gap-5">
            <div className="flex flex-col items-start justify-start gap-4">
              <div className="text-white self-stretch text-center font-['Graphik'] text-lg font-semibold leading-normal">
                You’re all clear! 🎉
              </div>
              <div className="w-[272px] text-center font-['Graphik'] text-xs font-normal leading-tight text-[#8d94a3]">
                There are no reported posts in your community
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
