import { useState } from 'react'

import { <PERSON><PERSON><PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Badge } from '@/components/ui'
import { cn } from '@/lib/utils'
import { mbtiTypes } from '@/lib/constants'

export function MBTIBadge({
  className,
  personalityType,
}: {
  className?: string
  personalityType: string
}) {
  const [dateTooltipOpen, setDateTooltipOpen] = useState(false)
  const description = mbtiTypes.find((type) => type.value === personalityType).description

  return (
    <TooltipProvider>
      <Tooltip open={dateTooltipOpen} onOpenChange={setDateTooltipOpen}>
        <TooltipTrigger
          asChild
          // Workaround from: https://github.com/radix-ui/primitives/issues/955#issuecomment-**********
          onClick={() => setDateTooltipOpen((prevOpen) => !prevOpen)}
          // Timeout runs setOpen after onO<PERSON>Change to prevent bug on mobile
          onFocus={() => setTimeout(() => setDateTooltipOpen(true), 0)}
          onBlur={() => setDateTooltipOpen(false)}
        >
          <Badge className={cn(className)} variant="secondary">
            {personalityType}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
