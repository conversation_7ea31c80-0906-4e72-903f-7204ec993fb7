.mention {
  text-decoration: none;
  border-radius: 14px;
  padding: 0.4rem;
}

.mentionSuggestions {
  /*   border-top: 1px solid #eee;
 */
  background: #fff;
  color: #000;
  border-radius: 12px;
  cursor: pointer;
  padding-top: 8px;
  padding-bottom: 8px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  transform-origin: 50% 0%;
  transform: scaleY(0);
  z-index: 1000;
  position: absolute;
  min-width: 200px;
  font-weight: bold;
  border: solid 1px #e2e2e4 !important;
}

.mentionSuggestionsDark {
  color: #ffffff;
  /* add your additional styles here */
  background: #17171a;
  border-radius: 12px;
  cursor: pointer;
  padding-top: 2px;
  padding-bottom: 2px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  transform-origin: 50% 0%;
  transform: scaleY(0);
  z-index: 1000;
  position: absolute;
  min-width: 200px;
  font-weight: bold;
  border: solid 1px #2a2b30 !important;
}

.mentionSuggestionsEntryContainer {
  display: table;
  width: 100%;
}

.mentionSuggestionsEntryContainerLeft,
.mentionSuggestionsEntryContainerRight {
  display: table-cell;
  vertical-align: middle;
}

.mentionSuggestionsEntryContainerRight {
  width: 100%;
  padding-left: 8px;
}

.mentionSuggestionsEntry {
  padding: 7px 10px 7px 10px;
  margin: 2px 3px;
  border-radius: 12px;
  transition: background-color 0.4s cubic-bezier(0.27, 1.27, 0.48, 0.56);
}

.mentionSuggestionsEntry:active {
  background-color: #434343;
}

.mentionSuggestionsEntryFocused {
  composes: mentionSuggestionsEntry;
  border-radius: 12px;
  background-color: #f8f9f9;
}

.mentionSuggestionsEntryFocusedDark {
  composes: mentionSuggestionsEntry;
  border-radius: 12px;
  background-color: #1c1c20;
}

.mentionSuggestionsEntryText,
.mentionSuggestionsEntryTitle {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mentionSuggestionsEntryText {
}

.mentionSuggestionsEntryTitle {
  font-size: 80%;
  color: #a7a7a7;
}

.mentionSuggestionsEntryAvatar {
  display: block;
  width: 30px;
  height: 30px;
  border-radius: 50%;
}
