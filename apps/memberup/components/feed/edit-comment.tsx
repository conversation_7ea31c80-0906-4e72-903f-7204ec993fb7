import { joi<PERSON>esolver } from '@hookform/resolvers/joi'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import FormControl from '@mui/material/FormControl'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import Joi from 'joi'
import React, { CSSProperties, useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { StreamMessage } from 'stream-chat-react'

import { convertToHtml, createEditorStateFromHtml } from '../../src/components/common/editor/editor-utils'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { updateFeedApi } from '@memberup/shared/src/services/apis/feed.api'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import MentionsInput from '@/components/feed/mentions-input'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import { mentionCreateEditorState, mentionGetEditorMentions, mentionGetSuggestions } from '@/memberup/libs/mentions'
import { unescapeSlashes } from '@/memberup/libs/utils'
import { setEditedCommentTracks } from '@/memberup/store/features/feedSlice'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { selectUser, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'

type FormDataType = {
  text: string
}

const FormValue: FormDataType = {
  text: '',
}

const FormSchema = Joi.object({
  text: Joi.string().required(),
}).options({ allowUnknown: true })

const EditComment: React.FC<{
  data?: StreamMessage | IFeed
  topLevelMessage: StreamMessage | IFeed
  parentMessage: StreamMessage | IFeed
  placeholder?: string
  members: { [key: string]: IUser }
  styles?: CSSProperties
  onClose?: () => void
  onCancel?: () => void
}> = ({ data, topLevelMessage, parentMessage, placeholder, members, styles, onClose, onCancel }) => {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const membership = useStore((state) => state.community.membership)
  const currentUser = useAppSelector((state) => selectUser(state))
  const currentUserProfile = useAppSelector((state) => selectUserProfile(state))
  const [requestComment, setRequestComment] = useState(false)
  const [mentions, setMentions] = useState([])
  const [editorState, setEditorState] = useState(null)
  const [formattedSuggestions, setFormattedSuggestions] = useState([])
  const dispatch = useAppDispatch()
  const isEditing = useRef(false)

  const { control, reset, formState, getValues, handleSubmit } = useForm<FormDataType>({
    mode: 'onBlur',
    reValidateMode: 'onChange',
    defaultValues: FormValue,
    resolver: joiResolver(FormSchema),
  })

  useEffect(() => {
    dispatch(setEditedCommentTracks({ id: data.id, operation: 'add' }))

    return () => {
      dispatch(setEditedCommentTracks({ id: data.id, operation: 'remove' }))
    }
  }, [])

  useEffect(() => {
    if (!isEditing.current) {
      const suggestions = mentionGetSuggestions(
        { ...members, [data.user.id]: data.user as IUser },
        currentUser,
        membership.id,
      )
      setFormattedSuggestions(suggestions)
      setEditorState(createEditorStateFromHtml(unescapeSlashes(data?.text || '')))
    }
  }, [data, members, isEditing])

  const handleInputChange = (field, editorCurrentContent) => {
    isEditing.current = true
    field.onChange(editorCurrentContent.getPlainText())
    const editorMentions = mentionGetEditorMentions(editorCurrentContent)
    if (JSON.stringify(mentions) !== JSON.stringify(editorMentions)) {
      setMentions(editorMentions)
    }
  }

  const resetEditor = () => {
    setEditorState(mentionCreateEditorState('', { ...members, [data.user.id]: data.user }))
  }

  const handleCancel = () => {
    resetEditor()
    onCancel?.()
  }

  const handleFormSubmit = (payload: FormDataType) => {
    try {
      isEditing.current = false
      setRequestComment(true)
      const featuredCommenters = (topLevelMessage.featured_commenters as any) || []

      /* check if this id is not already in the array */
      const isFeaturedCommenterInCommenters = featuredCommenters?.find((commenter) => commenter.id === currentUser.id)
      if (!isFeaturedCommenterInCommenters) {
        const featuredCommenterObj = {
          id: currentUser.id,
          name: getFullName(currentUser.first_name, currentUser.last_name, ''),
          image: currentUserProfile.image,
          image_crop_area: currentUserProfile.image_crop_area,
        }

        if (featuredCommenters?.length < 2) {
          featuredCommenters.push(featuredCommenterObj)
        } else if (featuredCommenters.length >= 2) {
          const random = Math.random()
          if (random < 0.5) {
            featuredCommenters[1] = featuredCommenterObj
          } else {
            featuredCommenters[2] = featuredCommenterObj
          }
        }
      }

      const filteredMentionedUsers = mentions.map((user) => {
        const { markup, ...rest } = user
        return rest
      })

      let hierarchyOrderString = ''
      if (topLevelMessage) {
        hierarchyOrderString = `${topLevelMessage?.reply_count || 0}`.padStart(4, '0')
      } else {
        if (!parentMessage.hierarchy_order) {
          hierarchyOrderString = null
        } else {
          let nextSeq = 0
          const parts = `${parentMessage.hierarchy_order}`.split('.')
          if (parts[1]) {
            const currentSeq = parseInt(parts[1])
            nextSeq = currentSeq + 1
          }
          hierarchyOrderString = `${parts[0]}.${`${nextSeq}`.padStart(4, '0')}`
        }
      }

      updateFeedApi(data.id, {
        text: convertToHtml(editorState),
        mentioned_users: filteredMentionedUsers || [],
        featured_commenters: featuredCommenters,
      } as any)
        .then((res) => {
          if (mountedRef.current) {
            resetEditor()
            onClose?.()
          }
        })
        .catch((err) => {
          if (err.response?.data?.error) {
            toast.error(err.response.data.error)
          }
        })
        .finally(() => {
          if (mountedRef.current) {
            setRequestComment(false)
          }
        })
      resetEditor()
    } catch (err: any) {
      console.log(err)
    }
  }

  return (
    <Box>
      {editorState && formattedSuggestions.length > 0 ? (
        <Box>
          <Box
            sx={{
              border: theme.palette.mode === 'dark' ? '1px solid #2A2B30' : '1px solid #E4E4E5',
              borderRadius: '16px',
              position: 'relative',
              p: '5px',
              mr: 0,
              mb: '3px',
            }}
          >
            <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
              <Controller
                render={({ field, fieldState: { error } }) => (
                  <FormControl className="form-control" error={Boolean(error)} fullWidth>
                    <MentionsInput
                      {...field}
                      editorState={editorState}
                      error={Boolean(error)}
                      formState={formState}
                      isPosting={requestComment}
                      placeholder={placeholder}
                      showLoadingSpinner={true}
                      styles={styles}
                      type="comment"
                      users={formattedSuggestions}
                      onCancel={handleCancel}
                      onChangeEditorState={setEditorState}
                      onChange={(editorState) => handleInputChange(field, editorState)}
                      onSubmit={() => handleSubmit(handleFormSubmit)}
                    />
                  </FormControl>
                )}
                control={control}
                name="text"
              />
            </form>
          </Box>
          <Box>
            <Typography variant="body2" component="span" color="text.disabled">
              Press Esc to&nbsp;
            </Typography>
            <Button className="no-padding" variant="text" onClick={handleCancel}>
              cancel
            </Button>
          </Box>
        </Box>
      ) : (
        <Box className="d-flex algin-center justify-center">
          <span>
            <LoadingSpinner />
          </span>
        </Box>
      )}
    </Box>
  )
}

EditComment.displayName = 'EditComment'
export default EditComment
