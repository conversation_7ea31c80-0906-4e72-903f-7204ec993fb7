import { Box, Grid, Typography } from '@mui/material'
import { Theme } from '@mui/material/styles'
import { makeStyles, useTheme } from '@mui/styles'
import React from 'react'
import ReactPlayer from 'react-player/lazy'

import ReadMoreContainer from '../../src/components/feed/read-more-container'
import { useRenderTextWithMentions } from '../../src/components/hooks/use-render-with-mentions'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import FeedAttachments from '@/components/feed/feed-attachments'
import PostLinks from '@/components/feed/post-links'
import { cn } from '@/lib/utils'
import { isUUIDv4 } from '@/memberup/libs/utils'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'

interface StylesProps {
  isPostPage: boolean
}

const useStyles = makeStyles<Theme, StylesProps>((theme) => ({
  container: {
    padding: (props) => (props.isPostPage ? '0px 3px' : '0px 20px'),
  },
  title: {
    opacity: 1,
    fontFamily: 'Graphik SemiBold',
    fontSize: '16px',
    fontWeight: 400,
    fontStyle: 'normal',
    letterSpacing: '0px',
    textAlign: 'left',
    lineHeight: '0',
  },
  content: {
    opacity: 1,
    '& a': {
      fontWeight: 'bold',
    },
    '& a:hover': {
      textDecoration: 'underline',
    },
  },
}))

export function PostDetailContent({
  className,
  feed,
  hasPostBeenRead,
  isPostPage = false,
}: {
  className?: string
  feed: IFeed
  hasPostBeenRead?: boolean
  isPostPage?: boolean
}) {
  const theme = useTheme()
  const classes = useStyles({ isPostPage })
  const membersMap = useAppSelector((state) => selectMembersMap(state))

  const renderedText = useRenderTextWithMentions(feed.text, feed?.mentioned_users, membersMap, 'single', feed)

  const topLink = Object.keys(feed.links || {})[0]
  const hasVideoLinkAtTheTop = topLink && ReactPlayer.canPlay(topLink)

  return (
    <div className={cn('post-detail-content h-auto px-5 md:h-full md:px-5', className)}>
      <Grid
        data-cy="post-title"
        container
        spacing={1}
        className={cn(classes.title, 'text-black-700 dark:text-white-500')}
        alignItems="center"
      >
        {!isUUIDv4(feed.title) && (
          <Grid item>
            <Typography
              className="text-black-700 dark:text-white-500"
              variant="subtitle1"
              gutterBottom
              style={{
                fontSize: '16px',
                fontWeight: '500',
                lineHeight: '24px',
                fontFamily: 'Graphik SemiBold',
                marginBottom: '10px',
              }}
            >
              {feed.title}
            </Typography>
          </Grid>
        )}
      </Grid>
      <Box className="text-sbase text-black-600 dark:text-white-200">
        {renderedText}
        <PostLinks post={feed} />
        <FeedAttachments feed={feed} />
      </Box>
    </div>
  )
}
