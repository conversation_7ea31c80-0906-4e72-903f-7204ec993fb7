import { <PERSON>, Card, CardContent, LinearProgress } from '@mui/material'
import useTheme from '@mui/material/styles/useTheme'
import _groupBy from 'lodash/groupBy'
import { useSearchParams } from 'next/navigation'
import React, { CSSProperties, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { StreamMessage, useChannelStateContext } from 'stream-chat-react'

import { ChevronLeft24Icon } from '../icons'
import { Comment } from './comment'
import { PostDetailContent } from './post-detail-content'
import { upsertFeedTrack } from '@memberup/shared/src/services/apis/feed-track.api'
import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import NewComment from '@/components/feed/new-comment'
import { PostHeader } from '@/components/feed/post-header'
import { PostSocialBar } from '@/components/feed/post-social-bar'
import { <PERSON><PERSON>, Separator } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { scrollIntoViewWithOffset } from '@/lib/ui'
import { cn } from '@/lib/utils'
import useAppStreamMessageListenerNew from '@/memberup/components/hooks/use-app-stream-message-listener-new'
import { addViewedFeed } from '@/memberup/store/features/feedTrackSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { RootState, useAppDispatch } from '@/memberup/store/store'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { IMembership, IUser } from '@/shared-types/interfaces'

interface PostDetailProps {
  closeModal?: () => void
  membership: IMembership
  feed: StreamMessage
  styleOverride?: CSSProperties
  isPostPage?: boolean
  setIsSubmittingComment?: (isSubmitting: boolean) => void
  onPinMessage: () => void
  onUnpinMessage: () => void
}

export function PostDetail({
  closeModal,
  membership,
  feed: message,
  styleOverride = null,
  isPostPage = false,
  setIsSubmittingComment = undefined,
  onPinMessage,
  onUnpinMessage,
}: PostDetailProps) {
  const dispatch = useAppDispatch()
  const rootEleRef = useRef(null)
  const theme = useTheme()
  const params = useSearchParams()
  const { channel: streamChatChannel } = useChannelStateContext()
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const initialMembersRef = useRef<{ [key: string]: IUser } | null>(null)
  const initialCommentsLoaded = useRef(false)

  const INITIAL_COMMENTS_FETCH_PAGE_SIZE = 100
  const SHOW_MORE_COMMENTS_FETCH_PAGE_SIZE = 100

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const { messages, updated, fetchMessages } = useAppStreamMessageListenerNew(
    true,
    'all',
    streamChatChannel,
    message,
    { feed_status: { $in: [FEED_STATUS_ENUM.active, FEED_STATUS_ENUM.approved] } },
    {
      limit: INITIAL_COMMENTS_FETCH_PAGE_SIZE,
      sort: { hierarchy_order: 1, created_at: 1 },
    },
  )

  const [loadingProgress, setLoadingProgress] = useState(0)
  const [isLoadingAllComments, setIsLoadingAllComments] = useState(false)

  const members = useAppSelector((state) => selectMembersMap(state))
  const feedTrackIds = useSelector((state: RootState) => state.feedTrack.feedTrackIds)
  const isPostOwner = message.user.id === userProfile?.user_id
  const feed = message

  const latestCommentTrackDate = feedTrackIds?.[feed.id] ? feedTrackIds[feed.id] : null
  const hasCommentsUnread = message.latest_comment_timestamp > latestCommentTrackDate

  const hasPostBeenRead = feedTrackIds?.[feed.id] || isPostOwner ? true : false

  useEffect(() => {
    if (!initialMembersRef?.current && Object.keys(members).length > 0) {
      initialMembersRef.current = members
    }
  }, [members])

  const scrollToBottom = () => {
    const appScrollArea = document.getElementById('app-scroll-area')
    if (!appScrollArea) {
      return
    }

    appScrollArea.scrollTo({
      top: appScrollArea.scrollHeight - appScrollArea.clientHeight,
      behavior: 'smooth',
    })
  }

  useEffect(() => {
    /* go to comment after clicking comment notification */
    const commentId = params.get('comment_id')

    if (commentId && messages?.results?.length > 0 && !initialCommentsLoaded.current) {
      setTimeout(() => {
        initialCommentsLoaded.current = true
        const commentElement = document.getElementById(commentId as string)

        if (commentElement) {
          scrollIntoViewWithOffset(commentElement, 100)
          const style = document.createElement('style')

          style.innerHTML = `
            @keyframes highlight {
              from {
                background-color: ${theme.palette.primary.main.replace(',1)', ',0.3)')};
              }
              to {
                background-color: transparent;
              }
            }
          `

          document.head.appendChild(style)

          commentElement.style.animation = 'highlight 2s'

          const handleAnimationEnd = () => {
            document.head.removeChild(style)
          }

          commentElement.addEventListener('animationend', handleAnimationEnd)

          // Cleanup function
          return () => {
            commentElement?.removeEventListener('animationend', handleAnimationEnd)
          }
        }
      }, 2000)
    }
  }, [messages])

  useEffect(() => {
    if (isLoadingAllComments) {
      const loadingProgress = (messages.results.length / feed.reply_count) * 100
      setLoadingProgress(loadingProgress)
      if (messages.next) {
        fetchMessages(false, SHOW_MORE_COMMENTS_FETCH_PAGE_SIZE)
      } else {
        setIsLoadingAllComments(false)
        setTimeout(() => {
          scrollToBottom()
        })
      }
    }
  }, [messages])

  useEffect(() => {
    if (!user) {
      return
    }

    if (hasCommentsUnread || !hasPostBeenRead) {
      const now = Math.floor(Date.now() / 1000)
      upsertFeedTrack({ feed_id: feed.id, updated_at: now })
      dispatch(addViewedFeed({ id: feed.id, updatedAt: now }))
    }
  }, [messages, message])

  const renderedComments = useMemo(() => {
    function isSecondLevelComment(item: any) {
      return item.hierarchy_order?.includes('.')
    }

    function isTopLevelComment(item: any) {
      return !isSecondLevelComment(item)
    }

    let temp = messages.results.length
      ? messages.next
        ? messages.results.slice(0, messages.results.length - 1)
        : messages.results
      : []

    // Some replies were converted from reactions and we need to use created_at_ext instead.
    temp = temp.map((r) => {
      const newObj = { ...r }
      newObj.created_at = r.createdAt || r.created_at_ext || r.created_at
      return newObj
    })

    // Enforce sorting as subsequential comments are added at the bottom of the list without respect to hierarchy_order.
    let repliesToOldComments = temp.filter((c) => c.reply_parent_id && !c.hierarchy_order)
    repliesToOldComments = repliesToOldComments.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    )

    const repliesToOldCommentsMap = _groupBy(repliesToOldComments, 'reply_parent_id')

    let oldTopLevelComments = temp.filter((c) => !c.reply_parent_id && !c.hierarchy_order)
    oldTopLevelComments = oldTopLevelComments.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    )

    let newCommentsWithHierarchy = temp.filter((c) => c.reply_parent_id && c.hierarchy_order)

    newCommentsWithHierarchy = newCommentsWithHierarchy.sort((a, b) =>
      a.hierarchy_order.localeCompare(b.hierarchy_order),
    )
    const allComments = oldTopLevelComments.concat(newCommentsWithHierarchy)

    if (!allComments.length) return null

    const processedCommentsIds = new Set()
    const commentsToRender = []
    for (const comment of allComments) {
      if (!comment.reply_parent_id) {
        processedCommentsIds.add(comment.id)
        commentsToRender.push(comment)
      } else if (comment.reply_parent_id === message.id || processedCommentsIds.has(comment.reply_parent_id)) {
        processedCommentsIds.add(comment.id)
        commentsToRender.push(comment)
      }
    }

    const repliesMap = _groupBy(commentsToRender, 'reply_parent_id')

    if (!commentsToRender.length) return null

    return (
      <>
        <Separator className="mb-5 bg-grey-200 dark:bg-grey-900" />
        <div className="space-y-5 px-4 pb-5 md:px-6">
          {commentsToRender.map(
            (comment) =>
              isTopLevelComment(comment) && (
                <div key={comment.id}>
                  <Comment
                    type="comment"
                    commentData={comment}
                    members={initialMembersRef?.current}
                    parentMessage={message}
                    replies={repliesMap[comment.id]}
                    repliesToOldComments={repliesToOldCommentsMap}
                  />
                </div>
              ),
          )}
        </div>
      </>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [(feed as unknown as IFeed)?.feed_status, messages.results, updated, members])

  let cardStyles = {
    overflow: 'unset',
    position: 'relative',
  }

  if (styleOverride) {
    cardStyles = {
      ...cardStyles,
      ...styleOverride,
    }
  }

  return (
    <>
      <Card
        className="feed-card rounded-container md:bg-white-700 h-full w-full overflow-hidden bg-transparent dark:md:bg-black-500"
        data-testid="post-detail"
        sx={cardStyles}
      >
        <CardContent
          className="feed-card-content h-full"
          sx={{
            '&:last-child': {
              paddingBottom: '0px',
            },
          }}
        >
          <Box
            className={cn(
              'feed-card-content-inner',
              !isPostPage &&
                'fixed left-0 top-0 flex h-full w-full flex-col md:relative md:left-auto md:top-auto md:h-auto md:max-h-[80vh] md:w-auto lg:mt-0 lg:max-h-[90vh]',
            )}
          >
            <div
              ref={rootEleRef}
              className={cn('post-details-container w-full flex-shrink', !isPostPage && 'grow overflow-y-auto')}
            >
              <div className="feed-card-header-container bg-white-700 sticky left-0 top-0 z-40 flex items-stretch bg-transparent p-4 dark:bg-black-700 md:p-5 md:dark:bg-black-500">
                {!isPostPage && (
                  <div className="md:hidden">
                    <Button
                      className="mr-3 mt-1.5 text-black-200 dark:text-black-100"
                      onClick={closeModal}
                      variant="inline"
                    >
                      <ChevronLeft24Icon />
                    </Button>
                  </div>
                )}
                <PostHeader
                  feed={feed as unknown as IFeed}
                  isSinglePost={true}
                  isPostPage={isPostPage}
                  userData={members[feed.user.id]}
                  onPinMessage={onPinMessage}
                  onUnpinMessage={onUnpinMessage}
                  membership={membership}
                />
              </div>
              <PostDetailContent
                isPostPage={isPostPage}
                feed={feed as unknown as IFeed}
                hasPostBeenRead={hasPostBeenRead}
              />
              <PostSocialBar
                className="mt-4 pb-5 md:px-5"
                message={feed as unknown as IFeed}
                isDetailView={true}
                hasCommentsUnread={hasCommentsUnread}
                members={members}
                readonly={!isUserAllowedToPost}
              />
              {renderedComments}
            </div>
            <Box
              className={cn(
                'card-new-comment bottom-0 z-30 w-full border-t border-grey-200 dark:border-grey-900',
                isPostPage && 'sticky',
              )}
              sx={{
                flex: '0 0 auto',
              }}
            >
              {isUserAllowedToPost && (
                <Box
                  sx={{
                    width: '100%',
                    position: { xs: 'relative', sm: 'auto' },
                  }}
                  data-testid="new-comment-container"
                  className="w-full rounded-l-base rounded-r-base px-4 py-4 dark:bg-black-700 md:bg-transparent md:px-6 dark:md:bg-black-500"
                >
                  <NewComment
                    isReply={false}
                    topLevelMessage={feed as unknown as IFeed}
                    members={(initialMembersRef?.current || []) as { [key: string]: IUser }}
                    addMentionByDefault={false}
                    parentPermalink={`/post/${feed?.permalink || feed?.id}`}
                    scrollToBottom={scrollToBottom}
                    setIsSubmittingComment={setIsSubmittingComment}
                    portal={true}
                  />
                </Box>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
      {loadingProgress > 0 && loadingProgress < 100 && <LinearProgress variant="determinate" value={loadingProgress} />}
    </>
  )
}
