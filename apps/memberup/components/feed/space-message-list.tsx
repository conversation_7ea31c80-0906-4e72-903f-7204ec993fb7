import { Grid } from '@mui/material'
import Divider from '@mui/material/Divider'
import React, { useMemo, useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'

import { FeedFilters } from '@/components/feed/feed-filters'
import NewPost from '@/components/feed/new-post'
import PostSkeleton from '@/components/feed/post-skeleton'
import { PostSummary } from '@/components/feed/post-summary'
import UndoHidePinnedPost from '@/components/feed/undo-hide-pinned-post'
import { SkeletonBox } from '@/components/ui'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { toast } from '@/components/ui/sonner'
import useFeed from '@/hooks/feed/use-feed'
import { useStore } from '@/hooks/useStore'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { deleteFeedApi } from '@/shared-services/apis/feed.api'
import SparkContainer from '@/src/components/spark/spark-container'
import { transformMessage } from '@/src/libs/utils'
import { selectFeedToDelete, setFeedToDelete } from '@/src/store/features/feedSlice'
import { useAppSelector } from '@/src/store/hooks'
import { useAppDispatch } from '@/src/store/store'

const SpaceEmptyPlaceholder = () => {
  return <PostSkeleton message={'No posts in this space.'} />
}

export const SpaceMessageList = () => {
  const dispatch = useAppDispatch()
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const membership = useStore((state) => state.community.membership)
  const [lastHiddenPinnedPost, setLastHiddenPinnedPost] = useState(null)
  const selectedPostIdToDelete = useAppSelector((state) => selectFeedToDelete(state))
  const [isDeletingPost, isRequestingDeletePost] = useState(false)
  const feed = useStore((state) => state.feed)
  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const {
    isLoadingMessages,
    isLoadingMore,
    messages,
    next,
    fetchMoreMessages,
    pinnedMessages,
    sortBy,
    spaceId,
    setNewSortBy,
    setNewSpaceId,
    isCommunitySpace,
    pinMessage,
    unpinMessage,
  } = useFeed()

  const handleOnSortByChange = (newSortBy) => {
    setNewSortBy(newSortBy)
  }

  const handleOnSpaceChange = (spaceId) => {
    setNewSpaceId(spaceId)
  }

  const handleOnHide = (postId: string) => {
    setLastHiddenPinnedPost(postId)
    toast.success('The pinned message will now be hidden from your Community space.', 'success')
  }

  const handleOnShow = () => {
    toast.success('The pinned message will now appear in your Community space.', 'success')
  }

  const handleFetchMore = () => {
    fetchMoreMessages()
  }

  const handlePinMessage = async (message) => {
    await pinMessage(message)
  }

  const handleUnpinMessage = async (message) => {
    await unpinMessage(message)
  }

  const filteredPinnedMessages = pinnedMessages
    .map(transformMessage)
    .filter((msg: any) => !userProfile?.pinned_posts_hidden?.[msg.id])

  const renderPinnedMessagesForManagement = useMemo(() => {
    if (pinnedMessages?.length === 0) {
      return <SpaceEmptyPlaceholder />
    }

    return (
      <>
        {pinnedMessages.map(transformMessage).map((item) => (
          <div key={item.id} style={{ marginBottom: '16px', width: '100%' }}>
            <PostSummary feed={item} onHide={() => handleOnHide(null)} onShow={handleOnShow} />
          </div>
        ))}

        {pinnedMessages.filter((msg: any) => !userProfile?.pinned_posts_hidden?.[msg.id]).length > 0 && (
          <Grid item xs={12} sx={{ marginBottom: '16px', marginTop: '16px' }}>
            <Divider
              className="background-color04"
              sx={{
                width: '100%',
                boxSizing: 'border-box',
                margin: 'auto',
                opacity: 0.3,
              }}
            />
          </Grid>
        )}
      </>
    )
  }, [pinnedMessages, userProfile?.pinned_posts_hidden])

  // const checkIfNoVisiblePinnedMessages = () => {
  //     if (pinnedMessages.length === 0) {
  //         return true
  //     }
  //
  //     const hiddenPinnedPostsIds = Object.entries(userProfile.pinned_posts_hidden || {})
  //         .filter((entry) => {
  //             const [key, value] = entry
  //             if (value) {
  //                 return true
  //             }
  //         })
  //         .map((e) => e[0])
  //     hiddenPinnedPostsIds.sort()
  //     const allPinnedPostsIds = pinnedMessages.map((m) => m.id).sort()
  //     return JSON.stringify(hiddenPinnedPostsIds) === JSON.stringify(allPinnedPostsIds)
  // }

  const showPostDeleteConfirmation = Boolean(selectedPostIdToDelete)

  const handleCancelDeletePost = () => {
    dispatch(setFeedToDelete(null))
  }

  const handleConfirmDeletePost = async () => {
    isRequestingDeletePost(true)
    try {
      await deleteFeedApi(selectedPostIdToDelete.id)
      const spaceId = selectedPostIdToDelete.cid.split(':')[1]
      feed.deleteMessage(spaceId, selectedPostIdToDelete.id)
      toast.success('Post deleted successfully')
    } catch (err) {
      toast.error(err.message)
    } finally {
      dispatch(setFeedToDelete(null))
      isRequestingDeletePost(false)
    }
  }

  const totalMessages = messages.length
  const hasMore = Boolean(next)

  return (
    <div>
      <ConfirmModal
        title="Delete post"
        open={showPostDeleteConfirmation}
        onClose={handleCancelDeletePost}
        onConfirm={handleConfirmDeletePost}
        loading={isDeletingPost}
      >
        Are you sure you want to delete this post?
      </ConfirmModal>

      {isUserAllowedToPost && <NewPost membership={membership} loading={!spaceId} />}
      <FeedFilters
        onSortByChange={handleOnSortByChange}
        onSpaceChange={handleOnSpaceChange}
        sortBy={sortBy}
        spaceConfig={spaceId}
        disabled={isLoadingMessages}
      />

      {isLoadingMessages ? (
        <SkeletonBox className="w-full" />
      ) : sortBy === 'pinned-posts' ? (
        renderPinnedMessagesForManagement
      ) : (
        <div id="post-list">
          {lastHiddenPinnedPost && (
            <UndoHidePinnedPost postId={lastHiddenPinnedPost} onUndo={() => setLastHiddenPinnedPost(null)} />
          )}
          {isCommunitySpace &&
            filteredPinnedMessages.map((item) => (
              <div key={item.id} className={'mb-4 w-full'}>
                <PostSummary
                  membership={membership}
                  onUnpinMessage={handleUnpinMessage}
                  onPinMessage={handlePinMessage}
                  feed={item}
                  onHide={handleOnHide}
                />
              </div>
            ))}

          {isCommunitySpace && <SparkContainer />}
          {!isLoadingMessages && messages.length === 0 && !lastHiddenPinnedPost && <SpaceEmptyPlaceholder />}
          <InfiniteScroll
            dataLength={totalMessages}
            next={handleFetchMore}
            hasMore={hasMore}
            loader={isLoadingMore ? <SkeletonBox className="w-full" /> : null}
            scrollableTarget="app-scroll-area-viewport"
            style={{ overflow: 'unset' }}
            initialScrollY={0}
          >
            <div>
              {messages.map(transformMessage).map((item) => (
                <div key={item.id} className="mb-4 w-full">
                  <PostSummary
                    membership={membership}
                    onUnpinMessage={handleUnpinMessage}
                    onPinMessage={handlePinMessage}
                    feed={item}
                    showPinnedMessageIndicator={false}
                  />
                </div>
              ))}
            </div>
          </InfiniteScroll>
        </div>
      )}
    </div>
  )
}
