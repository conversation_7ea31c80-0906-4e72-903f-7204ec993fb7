import Box from '@mui/material/Box'
import Dialog from '@mui/material/Dialog'
import DialogContent from '@mui/material/DialogContent'
import { makeStyles } from '@mui/styles'
import React, { useEffect } from 'react'
import { Channel, StreamMessage, useChatContext } from 'stream-chat-react'

import { Close24Icon } from '../icons'
import { PostDetail } from './post-detail'
import { Button } from '@/components/ui'
import { selectEditedCommentTracks } from '@/memberup/store/features/feedSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'
import { IMembership } from '@/shared-types/interfaces'

const useStyles = makeStyles(() => ({
  closeButton: {
    position: 'fixed',
    right: 40,
    top: 20,
    zIndex: 1000,
  },
  paper: {
    background: 'transparent',
    boxShadow: 'none', // Removes default shadow
  },
  backdrop: {
    backgroundColor: 'rgba(0, 0, 0, 0.0)', // Transparent background
  },
}))

export interface PostDetailModalProps {
  membership: IMembership
  feed: StreamMessage
  open: boolean
  onClose: () => void
  setOpenFeedDetails: (open: boolean) => void
  onPinMessage: (message: StreamMessage) => Promise<void>
  onUnpinMessage: (message: StreamMessage) => Promise<void>
}

export function PostDetailModal({
  membership,
  feed,
  open,
  onClose,
  setOpenFeedDetails,
  onPinMessage,
  onUnpinMessage,
}: PostDetailModalProps) {
  const classes = useStyles()
  const editedCommentTracks = useAppSelector(selectEditedCommentTracks)
  const dispatch = useAppDispatch()
  const [isSubmittingComment, setIsSubmittingComment] = React.useState(false)

  const { client } = useChatContext()
  const [channelType, channelId] = feed.cid.split(':')

  const channel = client.channel(channelType, channelId)

  useEffect(() => {
    const pathAfterDomain = window.location.pathname

    if (membership) {
      if (open && feed.permalink) {
        /* this avoids a page reload in contrast to react router replace */
        window.history.replaceState(null, '', `${membership.slug}/post/${feed.permalink || feed.id}`)
      } else {
        window.history.replaceState(null, '', `${membership.slug}/post/${feed.id}`)
      }
    }

    return () => {
      window.history.replaceState(null, '', pathAfterDomain)
    }
  }, [open, feed])

  const descriptionElementRef = React.useRef<HTMLElement>(null)
  React.useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef
      if (descriptionElement !== null) {
        descriptionElement.focus()
      }
    }
  }, [open])

  return (
    <Dialog
      open={open}
      onClick={(e) => {
        e.stopPropagation()
        if (isSubmittingComment) return

        const isUserEditingComments = Object.keys(editedCommentTracks).length > 0

        if (isUserEditingComments) {
          dispatch(
            openDialog({
              dialog: 'WarningEditingContent',
              open: true,
              props: { editingComments: true, setOpenFeedDetails },
            }),
          )
          return
        }
        /* check when clicking outside if the post is being edited */
        onClose()
      }}
      data-cy="feed-details-dialog"
      PaperProps={{ className: classes.paper }}
      fullScreen={true}
      TransitionProps={{
        in: open,
        timeout: {
          appear: 800,
          enter: 800,
          exit: 500,
        },
      }}
      style={{ zIndex: 1000 }}
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: 'transparent',
          height: '100%',
          width: '100%',
          maxHeight: '100%',
          maxWidth: '100%',
          margin: 0,
          padding: 0,
        },
      }}
      aria-labelledby="scroll-dialog-title"
      aria-describedby="scroll-dialog-description"
    >
      <div id="portal-suggestions-root" />
      <DialogContent
        className="bg-white-500 dark:bg-black-700 md:h-full md:bg-transparent md:dark:bg-transparent"
        id="feed-details-dialog-content"
        sx={{
          width: '100vw',
          padding: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          margin: 'auto',
          overflowY: 'hidden',
          '& .MuiPaper-root': {
            padding: 0,
          },
        }}
      >
        <Box
          id="feed-details-dialog-content-inner"
          className="w-full max-w-[720px] md:mx-[var(--page-container-padding)]"
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <Box
            id="scroll-dialog-description"
            ref={descriptionElementRef}
            sx={{
              '&:focusVisible': {
                outline: 'none',
              },
            }}
          >
            <Box
              className="feed-card-container d-flex flex-col justify-center md:max-h-[90vh]"
              sx={{
                borderRadius: 5,
                '& .str-chat.messaging': { backgroundColor: 'transparent' },
                '& .str-chat *': { fontFamily: '' },
                '& > div': {
                  maxHeight: '90vh',
                },
              }}
            >
              <Channel channel={channel}>
                <PostDetail
                  membership={membership}
                  feed={feed}
                  styleOverride={{
                    maxHeight: '90vh',
                    maxWidth: '720px',
                    margin: 'auto',
                  }}
                  setIsSubmittingComment={setIsSubmittingComment}
                  onPinMessage={onPinMessage}
                  onUnpinMessage={onUnpinMessage}
                  closeModal={onClose}
                />
              </Channel>
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <Button
        className="absolute right-8 top-8 hidden h-10 w-10 items-center justify-center rounded-full bg-white-200 text-black-200 transition-colors hover:bg-grey-400 dark:bg-black-700 dark:text-black-100 dark:hover:bg-grey-900 md:flex"
        variant="inline"
        onClick={onClose}
      >
        <Close24Icon />
      </Button>
    </Dialog>
  )
}
