'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
import { usePostHog } from 'posthog-js/react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { authenticate } from '@/app/actions/auth/authenticate'
import { ShimmerButton } from '@/components/magicui/shimmer-button'
import { Button, ControlVariants, Input, PasswordInput } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { UserEvents } from '@/lib/posthog'
import { removeQueryParam } from '@/lib/utils'
import { userSignUpSchema } from '@/lib/validation/user'
import { useAppDispatch } from '@/memberup/store/hooks'
import { getActiveUserSuccess } from '@/memberup/store/store'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'
import { getActiveUserApi } from '@/shared-services/apis/user.api'
import { AuthForms } from '@/store/authSlice'

type UserSignUpSchemaType = z.infer<typeof userSignUpSchema>

export function SignUpForm({ mode }: { mode: 'modal' | 'standalone' }) {
  const inviteToken = useStore((state) => state.auth.inviteToken)
  const setInviteToken = useStore((state) => state.auth.setInviteToken)
  const setShowForm = useStore((state) => state.auth.setShowForm)
  const setAuthenticatedUserData = useStore((state) => state.auth.setAuthenticatedUserData)
  const posthog = usePostHog()
  const searchParams = useSearchParams()
  const router = useRouter()
  const signUpForm = useForm<UserSignUpSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      first_name: '',
      last_name: '',
      email: '',
      password: '',
    },
    resolver: zodResolver(userSignUpSchema),
  })

  const dispatch = useAppDispatch()
  const [requestingSignUp, setRequestingSignUp] = useState(false)

  const onSignUpSubmit = async (formData: UserSignUpSchemaType) => {
    if (requestingSignUp) return
    setRequestingSignUp(true)

    try {
      const res = await authenticate({
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email,
        password: formData.password,
        time_zone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        is_signup: true,
        redirect: false,
        invite_token: inviteToken,
      })

      if (!res?.errorCode) {
        const {
          data: { data },
        } = await getActiveUserApi()

        if (inviteToken) {
          removeQueryParam('invite_token', searchParams, router)
          toast.success(`You have successfully joined the community!`)
          setInviteToken(null)
        }

        posthog.capture(UserEvents.SIGNED_UP)
        dispatch(getActiveUserSuccess(data))
        setAuthenticatedUserData(data)

        const nextUrl = searchParams.get('next')
        if (nextUrl) {
          try {
            // Validate the next URL to ensure it's internal
            const url = new URL(nextUrl, window.location.origin)
            if (url.origin === window.location.origin) {
              router.push(nextUrl)
              return
            }
          } catch {
            // Silently ignore invalid URLs
          }
        }

        router.push('/community')
      } else {
        if (res?.errorCode === 'email_already_exists') {
          signUpForm.setError('email', {
            type: 'custom',
            message: 'Email already in use',
          })
        } else {
          toast.error(res.message)
        }

        setRequestingSignUp(false)
      }
    } catch {
      toast.error(formSubmitError)
      setRequestingSignUp(false)
    }
  }

  const community = searchParams.get('community')
  const nextPath = searchParams.get('next')
  const loginLink = nextPath ? `/login?next=${nextPath}` : community ? `/login?community=${community}` : '/login'

  const controlVariant = mode === 'standalone' ? ControlVariants.transparent : ControlVariants.default

  return (
    <div className="flex flex-col items-center">
      <Image className="mb-6" src={memberupLogo} width={170} height={25} alt="MemberUp" />
      <div className="mb-8 text-center text-2xl font-semibold leading-6 text-white-500">
        Create Your MemberUp Account
      </div>
      <Form {...signUpForm}>
        <form autoComplete="off">
          <div className="flex w-full flex-col space-y-5">
            <FormField
              control={signUpForm.control}
              name="first_name"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      className="w-full"
                      disabled={requestingSignUp}
                      type="text"
                      placeholder="First Name"
                      variant={controlVariant}
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <FormField
              control={signUpForm.control}
              name="last_name"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      className="w-full"
                      disabled={requestingSignUp}
                      type="text"
                      placeholder="Last Name"
                      variant={controlVariant}
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <FormField
              control={signUpForm.control}
              name="email"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      autoComplete="off"
                      className="w-full"
                      disabled={requestingSignUp}
                      type="email"
                      placeholder="Email"
                      variant={controlVariant}
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <FormField
              control={signUpForm.control}
              name="password"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      className="w-full"
                      autoComplete="off"
                      placeholder="Password"
                      error={Boolean(error)}
                      variant={controlVariant}
                      disabled={requestingSignUp}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
          </div>
          <ShimmerButton
            className="mt-8 w-full"
            type="submit"
            loading={requestingSignUp}
            disabled={requestingSignUp}
            onClick={signUpForm.handleSubmit(onSignUpSubmit)}
            data-cy="signup-submit-button"
          >
            Sign Up
          </ShimmerButton>
          <div className="mt-5 text-sm text-black-200 dark:text-black-100">
            By signing up, you accept our{' '}
            <Link href="/terms-and-conditions" className="underline" target="_blank">
              terms
            </Link>{' '}
            and&nbsp;
            <Link href="/privacy-policy" className="underline" target="_blank">
              privacy policy
            </Link>
            .
          </div>
          <div className="mt-4 text-center text-sm text-gray-400 dark:text-black-100">
            Already have an account?&nbsp;
            {mode === 'standalone' ? (
              <Link className="text-underline text-primary-100 underline hover:text-primary-200" href={loginLink}>
                Log In
              </Link>
            ) : (
              <Button
                className="font-semibold text-primary-100 hover:text-primary-200"
                onClick={(e) => {
                  e.preventDefault()
                  setShowForm(AuthForms.login)
                }}
                variant="inline"
              >
                Log In
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  )
}
