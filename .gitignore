# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

*.tsbuildinfo

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage
__checks__/playwright/.auth
test-results

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem
storybook.log
migration-storybook.log

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging.local

# turbo
.turbo

.yarn

.vscode/settings.json
.vscode/extensions.json
exports

**/lessons.json
**/downloadable-lessons-error.log
